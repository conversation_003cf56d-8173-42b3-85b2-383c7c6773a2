{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/settings.vue?850c", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/settings.vue?ca53", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/settings.vue?96e5", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/settings.vue?38e4", "uni-app:///pages/profile/settings.vue", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/settings.vue?e9d9", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/profile/settings.vue?44e5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "userInfo", "settings", "jobPush", "systemNotify", "cacheSize", "onLoad", "methods", "loadUserInfo", "loadSettings", "savedSettings", "saveSettings", "storage", "editProfile", "uni", "url", "changePassword", "bindPhone", "onJobPushChange", "onSystemNotifyChange", "showPrivacyPolicy", "showUserAgreement", "clearCache", "confirmed", "checkUpdate", "feedback"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAs0B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6G11B;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA,gDACA,gBACAC,cACA;IACA;IAEA;IACAC;MACAC;IACA;IAEA;IACAC;MACA;QACAC;UACAC;QACA;QACA;MACA;MAEAD;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACAF;UACAC;QACA;QACA;MACA;MAEAD;QACAC;MACA;IACA;IAEA;IACAE;MACA;QACAH;UACAC;QACA;QACA;MACA;MAEAD;QACAC;MACA;IACA;IAEA;IACAG;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAN;QACAC;MACA;IACA;IAEA;IACAM;MACAP;QACAC;MACA;IACA;IAEA;IACAO;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACA;oBACA;oBACA;oBACA;oBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAX;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpPA;AAAA;AAAA;AAAA;AAA6jD,CAAgB,67CAAG,EAAC,C;;;;;;;;;;;ACAjlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/profile/settings.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/profile/settings.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./settings.vue?vue&type=template&id=058d5c7c&scoped=true&\"\nvar renderjs\nimport script from \"./settings.vue?vue&type=script&lang=js&\"\nexport * from \"./settings.vue?vue&type=script&lang=js&\"\nimport style0 from \"./settings.vue?vue&type=style&index=0&id=058d5c7c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"058d5c7c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/settings.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=template&id=058d5c7c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"settings-container\">\n    <!-- 账户设置 -->\n    <view class=\"settings-section\">\n      <view class=\"section-title\">账户设置</view>\n      <view class=\"settings-list\">\n        <view class=\"setting-item\" @click=\"editProfile\">\n          <view class=\"setting-info\">\n            <text class=\"setting-title\">个人资料</text>\n            <text class=\"setting-desc\">修改头像、姓名等信息</text>\n          </view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n        \n        <view class=\"setting-item\" @click=\"changePassword\">\n          <view class=\"setting-info\">\n            <text class=\"setting-title\">修改密码</text>\n            <text class=\"setting-desc\">更改登录密码</text>\n          </view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n        \n        <view class=\"setting-item\" @click=\"bindPhone\">\n          <view class=\"setting-info\">\n            <text class=\"setting-title\">绑定手机</text>\n            <text class=\"setting-desc\">{{ userInfo.phone || '未绑定' }}</text>\n          </view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 通知设置 -->\n    <view class=\"settings-section\">\n      <view class=\"section-title\">通知设置</view>\n      <view class=\"settings-list\">\n        <view class=\"setting-item\">\n          <view class=\"setting-info\">\n            <text class=\"setting-title\">新职位推送</text>\n            <text class=\"setting-desc\">接收匹配的职位推送</text>\n          </view>\n          <switch :checked=\"settings.jobPush\" @change=\"onJobPushChange\" color=\"#007aff\" />\n        </view>\n        \n        <view class=\"setting-item\">\n          <view class=\"setting-info\">\n            <text class=\"setting-title\">系统通知</text>\n            <text class=\"setting-desc\">接收系统消息通知</text>\n          </view>\n          <switch :checked=\"settings.systemNotify\" @change=\"onSystemNotifyChange\" color=\"#007aff\" />\n        </view>\n      </view>\n    </view>\n    \n    <!-- 隐私设置 -->\n    <view class=\"settings-section\">\n      <view class=\"section-title\">隐私设置</view>\n      <view class=\"settings-list\">\n        <view class=\"setting-item\" @click=\"showPrivacyPolicy\">\n          <view class=\"setting-info\">\n            <text class=\"setting-title\">隐私政策</text>\n            <text class=\"setting-desc\">查看隐私保护政策</text>\n          </view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n        \n        <view class=\"setting-item\" @click=\"showUserAgreement\">\n          <view class=\"setting-info\">\n            <text class=\"setting-title\">用户协议</text>\n            <text class=\"setting-desc\">查看用户服务协议</text>\n          </view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 其他设置 -->\n    <view class=\"settings-section\">\n      <view class=\"section-title\">其他</view>\n      <view class=\"settings-list\">\n        <view class=\"setting-item\" @click=\"clearCache\">\n          <view class=\"setting-info\">\n            <text class=\"setting-title\">清除缓存</text>\n            <text class=\"setting-desc\">{{ cacheSize }}</text>\n          </view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n        \n        <view class=\"setting-item\" @click=\"checkUpdate\">\n          <view class=\"setting-info\">\n            <text class=\"setting-title\">检查更新</text>\n            <text class=\"setting-desc\">当前版本 1.0.0</text>\n          </view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n        \n        <view class=\"setting-item\" @click=\"feedback\">\n          <view class=\"setting-info\">\n            <text class=\"setting-title\">意见反馈</text>\n            <text class=\"setting-desc\">帮助我们改进产品</text>\n          </view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { auth, showToast, showConfirm, storage } from '@/utils/utils.js'\n\nexport default {\n  name: 'Settings',\n  data() {\n    return {\n      userInfo: {},\n      settings: {\n        jobPush: true,\n        systemNotify: true\n      },\n      cacheSize: '12.5MB'\n    }\n  },\n  onLoad() {\n    this.loadUserInfo()\n    this.loadSettings()\n  },\n  methods: {\n    // 加载用户信息\n    loadUserInfo() {\n      this.userInfo = auth.getUserInfo() || {}\n    },\n    \n    // 加载设置\n    loadSettings() {\n      const savedSettings = storage.get('appSettings', {})\n      this.settings = {\n        ...this.settings,\n        ...savedSettings\n      }\n    },\n    \n    // 保存设置\n    saveSettings() {\n      storage.set('appSettings', this.settings)\n    },\n    \n    // 编辑个人资料\n    editProfile() {\n      if (!auth.isLoggedIn()) {\n        uni.navigateTo({\n          url: '/pages/auth/login'\n        })\n        return\n      }\n      \n      uni.navigateTo({\n        url: '/pages/profile/edit'\n      })\n    },\n    \n    // 修改密码\n    changePassword() {\n      if (!auth.isLoggedIn()) {\n        uni.navigateTo({\n          url: '/pages/auth/login'\n        })\n        return\n      }\n      \n      uni.navigateTo({\n        url: '/pages/profile/change-password'\n      })\n    },\n    \n    // 绑定手机\n    bindPhone() {\n      if (!auth.isLoggedIn()) {\n        uni.navigateTo({\n          url: '/pages/auth/login'\n        })\n        return\n      }\n      \n      uni.navigateTo({\n        url: '/pages/profile/bind-phone'\n      })\n    },\n    \n    // 职位推送开关变化\n    onJobPushChange(e) {\n      this.settings.jobPush = e.detail.value\n      this.saveSettings()\n      showToast(e.detail.value ? '已开启职位推送' : '已关闭职位推送')\n    },\n    \n    // 系统通知开关变化\n    onSystemNotifyChange(e) {\n      this.settings.systemNotify = e.detail.value\n      this.saveSettings()\n      showToast(e.detail.value ? '已开启系统通知' : '已关闭系统通知')\n    },\n    \n    // 显示隐私政策\n    showPrivacyPolicy() {\n      uni.navigateTo({\n        url: '/pages/agreement/privacy'\n      })\n    },\n    \n    // 显示用户协议\n    showUserAgreement() {\n      uni.navigateTo({\n        url: '/pages/agreement/user'\n      })\n    },\n    \n    // 清除缓存\n    async clearCache() {\n      const confirmed = await showConfirm('确定要清除缓存吗？')\n      if (confirmed) {\n        try {\n          // 清除图片缓存等\n          // uni.clearStorage() // 谨慎使用，会清除所有本地存储\n          showToast('缓存清除成功')\n          this.cacheSize = '0MB'\n        } catch (error) {\n          showToast('清除缓存失败')\n        }\n      }\n    },\n    \n    // 检查更新\n    checkUpdate() {\n      showToast('当前已是最新版本')\n    },\n    \n    // 意见反馈\n    feedback() {\n      uni.navigateTo({\n        url: '/pages/profile/feedback'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.settings-container {\n  min-height: 100vh;\n  background: #f8f9fa;\n  \n  .settings-section {\n    margin: 40rpx;\n    \n    .section-title {\n      font-size: 28rpx;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 24rpx;\n    }\n    \n    .settings-list {\n      background: #fff;\n      border-radius: 16rpx;\n      overflow: hidden;\n      \n      .setting-item {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding: 32rpx;\n        border-bottom: 2rpx solid #f8f9fa;\n        \n        &:last-child {\n          border-bottom: none;\n        }\n        \n        .setting-info {\n          flex: 1;\n          \n          .setting-title {\n            display: block;\n            font-size: 28rpx;\n            color: #333;\n            margin-bottom: 8rpx;\n          }\n          \n          .setting-desc {\n            display: block;\n            font-size: 24rpx;\n            color: #999;\n          }\n        }\n        \n        .iconfont {\n          font-size: 24rpx;\n          color: #ccc;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=style&index=0&id=058d5c7c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=style&index=0&id=058d5c7c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756715321647\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}