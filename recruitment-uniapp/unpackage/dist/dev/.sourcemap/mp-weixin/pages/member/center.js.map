{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/member/center.vue?28e7", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/member/center.vue?9c49", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/member/center.vue?85cf", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/member/center.vue?47c8", "uni-app:///pages/member/center.vue", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/member/center.vue?4657", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/member/center.vue?ebfb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "userInfo", "membershipInfo", "isMember", "remainingViews", "expireTime", "packages", "type", "price", "originalPrice", "desc", "recommended", "benefits", "selected<PERSON><PERSON><PERSON>", "showPaymentModal", "buyLoading", "payLoading", "onLoad", "onShow", "methods", "formatDate", "loadUserInfo", "loadMembershipInfo", "paymentApi", "res", "console", "selectPackage", "buyPackage", "uni", "url", "confirmPayment", "wechatPay", "provider", "timeStamp", "nonceStr", "package", "signType", "paySign", "success", "resolve", "fail", "reject", "goToConsumptionRecords", "goToMemberRights", "contactService", "title", "content", "confirmText"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAo0B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACqJx1B;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC,WACA;QACAC;QACAR;QACAS;QACAC;QACAC;QACAC;QACAC,WACA,YACA,UACA;MAEA,GACA;QACAL;QACAR;QACAS;QACAC;QACAC;QACAC;QACAC,WACA,YACA,UACA,UACA;MAEA,EACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACAC;UACAC;QACA;QACA;MACA;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBAAA;gBAAA,OAEAP;cAAA;gBAAAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAM;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACAH;oBACAI;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;sBACA;sBACA;sBACAC;oBACA;oBACAC;sBACA;wBACA;sBACA;sBACAC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACAd;QACAC;MACA;IACA;IAEA;IACAc;MACAf;QACAC;MACA;IACA;IAEA;IACAe;MACAhB;QACAiB;QACAC;QACAC;QACAT;UACA;YACAV;cACA5B;cACAsC;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrUA;AAAA;AAAA;AAAA;AAA2jD,CAAgB,27CAAG,EAAC,C;;;;;;;;;;;ACA/kD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/member/center.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/member/center.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./center.vue?vue&type=template&id=1af0b8c1&scoped=true&\"\nvar renderjs\nimport script from \"./center.vue?vue&type=script&lang=js&\"\nexport * from \"./center.vue?vue&type=script&lang=js&\"\nimport style0 from \"./center.vue?vue&type=style&index=0&id=1af0b8c1&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1af0b8c1\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/member/center.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./center.vue?vue&type=template&id=1af0b8c1&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.membershipInfo.isMember\n    ? _vm.formatDate(_vm.membershipInfo.expireTime)\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showPaymentModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./center.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./center.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"member-center\">\n    <!-- 会员状态卡片 -->\n    <view class=\"member-card\">\n      <view class=\"card-bg\">\n        <image src=\"/static/member-bg.png\" mode=\"aspectFill\"></image>\n      </view>\n      <view class=\"card-content\">\n        <view class=\"member-info\">\n          <view class=\"avatar\">\n            <image :src=\"userInfo.avatar || '/static/default-avatar.png'\" mode=\"aspectFill\"></image>\n          </view>\n          <view class=\"user-info\">\n            <text class=\"username\">{{ userInfo.name || '用户' }}</text>\n            <text class=\"member-status\">{{ membershipInfo.isMember ? '会员用户' : '普通用户' }}</text>\n          </view>\n        </view>\n        \n        <view v-if=\"membershipInfo.isMember\" class=\"member-benefits\">\n          <view class=\"benefit-item\">\n            <text class=\"benefit-label\">剩余次数</text>\n            <text class=\"benefit-value\">{{ membershipInfo.remainingViews }}次</text>\n          </view>\n          <view class=\"benefit-item\">\n            <text class=\"benefit-label\">到期时间</text>\n            <text class=\"benefit-value\">{{ formatDate(membershipInfo.expireTime) }}</text>\n          </view>\n        </view>\n        \n        <view v-else class=\"upgrade-tip\">\n          <text class=\"tip-text\">开通会员，享受更多特权</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 会员套餐 -->\n    <view class=\"membership-packages\">\n      <view class=\"section-title\">会员套餐</view>\n      <view class=\"packages-list\">\n        <view \n          v-for=\"pkg in packages\" \n          :key=\"pkg.type\"\n          class=\"package-item\"\n          :class=\"{ recommended: pkg.recommended }\"\n          @click=\"selectPackage(pkg)\"\n        >\n          <view v-if=\"pkg.recommended\" class=\"recommended-tag\">推荐</view>\n          <view class=\"package-header\">\n            <text class=\"package-name\">{{ pkg.name }}</text>\n            <view class=\"package-price\">\n              <text class=\"price\">¥{{ pkg.price }}</text>\n              <text class=\"original-price\" v-if=\"pkg.originalPrice\">¥{{ pkg.originalPrice }}</text>\n            </view>\n          </view>\n          <view class=\"package-benefits\">\n            <view \n              v-for=\"benefit in pkg.benefits\" \n              :key=\"benefit\"\n              class=\"benefit-item\"\n            >\n              <text class=\"iconfont icon-check\"></text>\n              <text class=\"benefit-text\">{{ benefit }}</text>\n            </view>\n          </view>\n          <button \n            class=\"buy-btn\" \n            :class=\"{ primary: pkg.recommended }\"\n            @click.stop=\"buyPackage(pkg)\"\n            :loading=\"buyLoading === pkg.type\"\n          >\n            {{ membershipInfo.isMember ? '续费' : '立即开通' }}\n          </button>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 功能入口 -->\n    <view class=\"function-entries\">\n      <view class=\"entry-item\" @click=\"goToConsumptionRecords\">\n        <view class=\"entry-icon\">\n          <text class=\"iconfont icon-bill\"></text>\n        </view>\n        <view class=\"entry-info\">\n          <text class=\"entry-title\">消费记录</text>\n          <text class=\"entry-desc\">查看付费记录和剩余次数</text>\n        </view>\n        <text class=\"iconfont icon-arrow-right\"></text>\n      </view>\n      \n      <view class=\"entry-item\" @click=\"goToMemberRights\">\n        <view class=\"entry-icon\">\n          <text class=\"iconfont icon-vip\"></text>\n        </view>\n        <view class=\"entry-info\">\n          <text class=\"entry-title\">会员权益</text>\n          <text class=\"entry-desc\">了解会员专享特权</text>\n        </view>\n        <text class=\"iconfont icon-arrow-right\"></text>\n      </view>\n      \n      <view class=\"entry-item\" @click=\"contactService\">\n        <view class=\"entry-icon\">\n          <text class=\"iconfont icon-service\"></text>\n        </view>\n        <view class=\"entry-info\">\n          <text class=\"entry-title\">客服咨询</text>\n          <text class=\"entry-desc\">遇到问题？联系客服</text>\n        </view>\n        <text class=\"iconfont icon-arrow-right\"></text>\n      </view>\n    </view>\n    \n    <!-- 支付确认弹窗 -->\n    <u-popup v-model=\"showPaymentModal\" mode=\"center\" width=\"80%\" border-radius=\"20\">\n      <view class=\"payment-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">确认支付</text>\n          <text class=\"close-btn\" @click=\"showPaymentModal = false\">×</text>\n        </view>\n        \n        <view class=\"payment-info\">\n          <view class=\"package-info\">\n            <text class=\"package-name\">{{ selectedPackage.name }}</text>\n            <text class=\"package-desc\">{{ selectedPackage.desc }}</text>\n          </view>\n          <view class=\"price-info\">\n            <text class=\"price\">¥{{ selectedPackage.price }}</text>\n            <text class=\"original-price\" v-if=\"selectedPackage.originalPrice\">¥{{ selectedPackage.originalPrice }}</text>\n          </view>\n        </view>\n        \n        <view class=\"payment-methods\">\n          <view class=\"method-title\">支付方式</view>\n          <view class=\"method-item active\">\n            <text class=\"iconfont icon-wechat-pay\"></text>\n            <text class=\"method-name\">微信支付</text>\n            <text class=\"iconfont icon-check\"></text>\n          </view>\n        </view>\n        \n        <button class=\"confirm-pay-btn\" @click=\"confirmPayment\" :loading=\"payLoading\">\n          确认支付 ¥{{ selectedPackage.price }}\n        </button>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport { paymentApi, authApi } from '@/utils/api.js'\nimport { auth, formatDate, showToast, showLoading, hideLoading } from '@/utils/utils.js'\n\nexport default {\n  name: 'MemberCenter',\n  data() {\n    return {\n      userInfo: {},\n      membershipInfo: {\n        isMember: false,\n        remainingViews: 0,\n        expireTime: null\n      },\n      packages: [\n        {\n          type: 'basic',\n          name: '基础会员',\n          price: 30,\n          originalPrice: null,\n          desc: '查看45个联系人',\n          recommended: false,\n          benefits: [\n            '查看45个联系人',\n            '30天有效期',\n            '专属客服支持'\n          ]\n        },\n        {\n          type: 'premium',\n          name: '高级会员',\n          price: 50,\n          originalPrice: 80,\n          desc: '查看80个联系人',\n          recommended: true,\n          benefits: [\n            '查看80个联系人',\n            '60天有效期',\n            '优先客服支持',\n            '专属求职指导'\n          ]\n        }\n      ],\n      selectedPackage: {},\n      showPaymentModal: false,\n      buyLoading: '',\n      payLoading: false\n    }\n  },\n  onLoad() {\n    this.loadUserInfo()\n    this.loadMembershipInfo()\n  },\n  onShow() {\n    // 从支付页面返回时刷新数据\n    this.loadMembershipInfo()\n  },\n  methods: {\n    formatDate,\n    \n    // 加载用户信息\n    loadUserInfo() {\n      this.userInfo = auth.getUserInfo() || {}\n    },\n    \n    // 加载会员信息\n    async loadMembershipInfo() {\n      try {\n        const res = await paymentApi.getMembershipInfo()\n        if (res.code === 200) {\n          this.membershipInfo = res.data\n        }\n      } catch (error) {\n        console.error('加载会员信息失败:', error)\n      }\n    },\n    \n    // 选择套餐\n    selectPackage(pkg) {\n      this.selectedPackage = pkg\n    },\n    \n    // 购买套餐\n    buyPackage(pkg) {\n      if (!auth.isLoggedIn()) {\n        uni.navigateTo({\n          url: '/pages/auth/login'\n        })\n        return\n      }\n      \n      this.selectedPackage = pkg\n      this.showPaymentModal = true\n    },\n    \n    // 确认支付\n    async confirmPayment() {\n      if (this.payLoading) return\n      \n      try {\n        this.payLoading = true\n        \n        const res = await paymentApi.buyMembership(this.selectedPackage.type)\n        \n        if (res.code === 200) {\n          // 调用微信支付\n          await this.wechatPay(res.data)\n          this.showPaymentModal = false\n        } else {\n          showToast(res.msg || '支付失败')\n        }\n      } catch (error) {\n        console.error('支付失败:', error)\n        showToast('支付失败，请重试')\n      } finally {\n        this.payLoading = false\n      }\n    },\n    \n    // 微信支付\n    async wechatPay(paymentData) {\n      return new Promise((resolve, reject) => {\n        uni.requestPayment({\n          provider: 'wxpay',\n          timeStamp: paymentData.timeStamp,\n          nonceStr: paymentData.nonceStr,\n          package: paymentData.package,\n          signType: paymentData.signType,\n          paySign: paymentData.paySign,\n          success: (res) => {\n            showToast('支付成功', 'success')\n            this.loadMembershipInfo()\n            resolve(res)\n          },\n          fail: (err) => {\n            if (err.errMsg !== 'requestPayment:fail cancel') {\n              showToast('支付失败')\n            }\n            reject(err)\n          }\n        })\n      })\n    },\n    \n    // 跳转到消费记录\n    goToConsumptionRecords() {\n      uni.navigateTo({\n        url: '/pages/member/consumption-records'\n      })\n    },\n    \n    // 跳转到会员权益\n    goToMemberRights() {\n      uni.navigateTo({\n        url: '/pages/member/rights'\n      })\n    },\n    \n    // 联系客服\n    contactService() {\n      uni.showModal({\n        title: '客服咨询',\n        content: '请添加客服微信：kf888888',\n        confirmText: '复制微信号',\n        success: (res) => {\n          if (res.confirm) {\n            uni.setClipboardData({\n              data: 'kf888888',\n              success: () => {\n                showToast('微信号已复制')\n              }\n            })\n          }\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.member-center {\n  min-height: 100vh;\n  background: #f8f9fa;\n  \n  .member-card {\n    position: relative;\n    margin: 40rpx;\n    border-radius: 24rpx;\n    overflow: hidden;\n    \n    .card-bg {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      \n      image {\n        width: 100%;\n        height: 100%;\n      }\n    }\n    \n    .card-content {\n      position: relative;\n      padding: 40rpx;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      \n      .member-info {\n        display: flex;\n        align-items: center;\n        margin-bottom: 40rpx;\n        \n        .avatar {\n          width: 80rpx;\n          height: 80rpx;\n          border-radius: 40rpx;\n          overflow: hidden;\n          margin-right: 24rpx;\n          \n          image {\n            width: 100%;\n            height: 100%;\n          }\n        }\n        \n        .user-info {\n          .username {\n            display: block;\n            font-size: 32rpx;\n            font-weight: 600;\n            color: #fff;\n            margin-bottom: 8rpx;\n          }\n          \n          .member-status {\n            display: block;\n            font-size: 24rpx;\n            color: rgba(255, 255, 255, 0.8);\n          }\n        }\n      }\n      \n      .member-benefits {\n        display: flex;\n        justify-content: space-between;\n        \n        .benefit-item {\n          text-align: center;\n          \n          .benefit-label {\n            display: block;\n            font-size: 24rpx;\n            color: rgba(255, 255, 255, 0.8);\n            margin-bottom: 8rpx;\n          }\n          \n          .benefit-value {\n            display: block;\n            font-size: 28rpx;\n            font-weight: 600;\n            color: #fff;\n          }\n        }\n      }\n      \n      .upgrade-tip {\n        text-align: center;\n        \n        .tip-text {\n          font-size: 28rpx;\n          color: rgba(255, 255, 255, 0.9);\n        }\n      }\n    }\n  }\n  \n  .membership-packages {\n    margin: 40rpx;\n    \n    .section-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 32rpx;\n    }\n    \n    .packages-list {\n      display: flex;\n      gap: 24rpx;\n      \n      .package-item {\n        flex: 1;\n        position: relative;\n        background: #fff;\n        border-radius: 16rpx;\n        padding: 32rpx;\n        border: 2rpx solid #e9ecef;\n        \n        &.recommended {\n          border-color: #007aff;\n          box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.15);\n        }\n        \n        .recommended-tag {\n          position: absolute;\n          top: -2rpx;\n          right: 32rpx;\n          background: #007aff;\n          color: #fff;\n          font-size: 20rpx;\n          padding: 8rpx 16rpx;\n          border-radius: 0 0 12rpx 12rpx;\n        }\n        \n        .package-header {\n          text-align: center;\n          margin-bottom: 32rpx;\n          \n          .package-name {\n            display: block;\n            font-size: 28rpx;\n            font-weight: 600;\n            color: #333;\n            margin-bottom: 16rpx;\n          }\n          \n          .package-price {\n            .price {\n              font-size: 48rpx;\n              font-weight: 600;\n              color: #ff4757;\n            }\n            \n            .original-price {\n              font-size: 24rpx;\n              color: #999;\n              text-decoration: line-through;\n              margin-left: 16rpx;\n            }\n          }\n        }\n        \n        .package-benefits {\n          margin-bottom: 32rpx;\n          \n          .benefit-item {\n            display: flex;\n            align-items: center;\n            margin-bottom: 16rpx;\n            \n            .iconfont {\n              font-size: 24rpx;\n              color: #07c160;\n              margin-right: 12rpx;\n            }\n            \n            .benefit-text {\n              font-size: 24rpx;\n              color: #666;\n            }\n          }\n        }\n        \n        .buy-btn {\n          width: 100%;\n          height: 72rpx;\n          background: #f8f9fa;\n          color: #666;\n          border: 2rpx solid #e9ecef;\n          border-radius: 36rpx;\n          font-size: 28rpx;\n          \n          &.primary {\n            background: #007aff;\n            color: #fff;\n            border-color: #007aff;\n          }\n        }\n      }\n    }\n  }\n  \n  .function-entries {\n    margin: 40rpx;\n    background: #fff;\n    border-radius: 16rpx;\n    overflow: hidden;\n    \n    .entry-item {\n      display: flex;\n      align-items: center;\n      padding: 32rpx;\n      border-bottom: 2rpx solid #f8f9fa;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .entry-icon {\n        width: 80rpx;\n        height: 80rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #f0f8ff;\n        border-radius: 40rpx;\n        margin-right: 24rpx;\n        \n        .iconfont {\n          font-size: 36rpx;\n          color: #007aff;\n        }\n      }\n      \n      .entry-info {\n        flex: 1;\n        \n        .entry-title {\n          display: block;\n          font-size: 28rpx;\n          font-weight: 500;\n          color: #333;\n          margin-bottom: 8rpx;\n        }\n        \n        .entry-desc {\n          display: block;\n          font-size: 24rpx;\n          color: #999;\n        }\n      }\n      \n      .icon-arrow-right {\n        font-size: 24rpx;\n        color: #ccc;\n      }\n    }\n  }\n}\n\n.payment-modal {\n  padding: 40rpx;\n  \n  .modal-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 40rpx;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333;\n    }\n    \n    .close-btn {\n      font-size: 48rpx;\n      color: #999;\n      line-height: 1;\n    }\n  }\n  \n  .payment-info {\n    margin-bottom: 40rpx;\n    \n    .package-info {\n      text-align: center;\n      margin-bottom: 24rpx;\n      \n      .package-name {\n        display: block;\n        font-size: 28rpx;\n        font-weight: 600;\n        color: #333;\n        margin-bottom: 8rpx;\n      }\n      \n      .package-desc {\n        display: block;\n        font-size: 24rpx;\n        color: #666;\n      }\n    }\n    \n    .price-info {\n      text-align: center;\n      \n      .price {\n        font-size: 48rpx;\n        font-weight: 600;\n        color: #ff4757;\n      }\n      \n      .original-price {\n        font-size: 24rpx;\n        color: #999;\n        text-decoration: line-through;\n        margin-left: 16rpx;\n      }\n    }\n  }\n  \n  .payment-methods {\n    margin-bottom: 40rpx;\n    \n    .method-title {\n      font-size: 28rpx;\n      color: #333;\n      margin-bottom: 24rpx;\n    }\n    \n    .method-item {\n      display: flex;\n      align-items: center;\n      padding: 24rpx;\n      background: #f8f9fa;\n      border-radius: 12rpx;\n      border: 2rpx solid #e9ecef;\n      \n      &.active {\n        border-color: #007aff;\n        background: #f0f8ff;\n      }\n      \n      .iconfont {\n        font-size: 32rpx;\n        color: #07c160;\n        margin-right: 16rpx;\n      }\n      \n      .method-name {\n        flex: 1;\n        font-size: 28rpx;\n        color: #333;\n      }\n      \n      .icon-check {\n        font-size: 24rpx;\n        color: #007aff;\n      }\n    }\n  }\n  \n  .confirm-pay-btn {\n    width: 100%;\n    height: 88rpx;\n    background: #007aff;\n    color: #fff;\n    border: none;\n    border-radius: 44rpx;\n    font-size: 32rpx;\n    font-weight: 500;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./center.vue?vue&type=style&index=0&id=1af0b8c1&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./center.vue?vue&type=style&index=0&id=1af0b8c1&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756715321656\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}