@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.job-detail-container.data-v-dcc1a2c8 {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}
.job-detail-container .job-header.data-v-dcc1a2c8 {
  background: #fff;
  padding: 40rpx;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.job-detail-container .job-header .job-info.data-v-dcc1a2c8 {
  flex: 1;
}
.job-detail-container .job-header .job-info .job-title.data-v-dcc1a2c8 {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.job-detail-container .job-header .job-info .salary.data-v-dcc1a2c8 {
  display: block;
  font-size: 32rpx;
  color: #ff4757;
  font-weight: 600;
  margin-bottom: 24rpx;
}
.job-detail-container .job-header .job-info .job-tags.data-v-dcc1a2c8 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.job-detail-container .job-header .job-info .job-tags .tag.data-v-dcc1a2c8 {
  padding: 8rpx 16rpx;
  background: #f0f8ff;
  color: #007aff;
  font-size: 24rpx;
  border-radius: 8rpx;
}
.job-detail-container .job-header .company-logo.data-v-dcc1a2c8 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 16rpx;
  overflow: hidden;
}
.job-detail-container .job-header .company-logo image.data-v-dcc1a2c8 {
  width: 100%;
  height: 100%;
}
.job-detail-container .job-requirements.data-v-dcc1a2c8 {
  background: #fff;
  padding: 32rpx 40rpx;
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 32rpx;
}
.job-detail-container .job-requirements .requirement-item.data-v-dcc1a2c8 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}
.job-detail-container .job-requirements .requirement-item .iconfont.data-v-dcc1a2c8 {
  font-size: 28rpx;
  margin-right: 12rpx;
  color: #999;
}
.job-detail-container .company-info.data-v-dcc1a2c8 {
  background: #fff;
  padding: 32rpx 40rpx;
  margin-top: 20rpx;
}
.job-detail-container .company-info .company-header.data-v-dcc1a2c8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.job-detail-container .company-info .company-header .company-basic .company-name.data-v-dcc1a2c8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}
.job-detail-container .company-info .company-header .company-basic .company-scale.data-v-dcc1a2c8 {
  font-size: 24rpx;
  color: #666;
}
.job-detail-container .company-info .company-header .view-company.data-v-dcc1a2c8 {
  font-size: 28rpx;
  color: #007aff;
}
.job-detail-container .company-info .company-desc.data-v-dcc1a2c8 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.job-detail-container .job-description.data-v-dcc1a2c8, .job-detail-container .job-requirements-detail.data-v-dcc1a2c8 {
  background: #fff;
  padding: 32rpx 40rpx;
  margin-top: 20rpx;
}
.job-detail-container .job-description .section-title.data-v-dcc1a2c8, .job-detail-container .job-requirements-detail .section-title.data-v-dcc1a2c8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}
.job-detail-container .job-description .description-content.data-v-dcc1a2c8, .job-detail-container .job-description .requirements-content.data-v-dcc1a2c8, .job-detail-container .job-requirements-detail .description-content.data-v-dcc1a2c8, .job-detail-container .job-requirements-detail .requirements-content.data-v-dcc1a2c8 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
}
.job-detail-container .work-address.data-v-dcc1a2c8 {
  background: #fff;
  padding: 32rpx 40rpx;
  margin-top: 20rpx;
}
.job-detail-container .work-address .section-title.data-v-dcc1a2c8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}
.job-detail-container .work-address .address-info.data-v-dcc1a2c8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.job-detail-container .work-address .address-info .address-text.data-v-dcc1a2c8 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}
.job-detail-container .work-address .address-info .address-text .iconfont.data-v-dcc1a2c8 {
  font-size: 28rpx;
  margin-right: 12rpx;
  color: #999;
}
.job-detail-container .work-address .address-info .view-map.data-v-dcc1a2c8 {
  font-size: 28rpx;
  color: #007aff;
}
.job-detail-container .publish-info.data-v-dcc1a2c8 {
  background: #fff;
  padding: 32rpx 40rpx;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}
.job-detail-container .bottom-actions.data-v-dcc1a2c8 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 24rpx 40rpx;
  background: #fff;
  border-top: 2rpx solid #f0f0f0;
}
.job-detail-container .bottom-actions .collect-btn.data-v-dcc1a2c8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 80rpx;
  background: transparent;
  border: none;
  font-size: 24rpx;
  color: #666;
  margin-right: 32rpx;
}
.job-detail-container .bottom-actions .collect-btn.collected.data-v-dcc1a2c8 {
  color: #ff4757;
}
.job-detail-container .bottom-actions .collect-btn .iconfont.data-v-dcc1a2c8 {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.job-detail-container .bottom-actions .contact-btn.data-v-dcc1a2c8 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}
.job-detail-container .bottom-actions .contact-btn .iconfont.data-v-dcc1a2c8 {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.contact-modal.data-v-dcc1a2c8 {
  padding: 40rpx;
}
.contact-modal .modal-header.data-v-dcc1a2c8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.contact-modal .modal-header .modal-title.data-v-dcc1a2c8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.contact-modal .modal-header .close-btn.data-v-dcc1a2c8 {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}
.contact-modal .contact-content .contact-item.data-v-dcc1a2c8 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.contact-modal .contact-content .contact-item .label.data-v-dcc1a2c8 {
  width: 120rpx;
  font-size: 28rpx;
  color: #666;
}
.contact-modal .contact-content .contact-item .value.data-v-dcc1a2c8 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.contact-modal .contact-content .contact-item .value.phone.data-v-dcc1a2c8 {
  color: #007aff;
  text-decoration: underline;
}
.contact-modal .payment-content.data-v-dcc1a2c8 {
  text-align: center;
}
.contact-modal .payment-content .payment-info.data-v-dcc1a2c8 {
  margin-bottom: 40rpx;
}
.contact-modal .payment-content .payment-info .payment-title.data-v-dcc1a2c8 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.contact-modal .payment-content .payment-info .payment-desc.data-v-dcc1a2c8 {
  display: block;
  font-size: 28rpx;
  color: #666;
}
.contact-modal .payment-content .membership-tip.data-v-dcc1a2c8 {
  margin-bottom: 40rpx;
}
.contact-modal .payment-content .membership-tip .tip-text.data-v-dcc1a2c8 {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 24rpx;
}
.contact-modal .payment-content .membership-tip .membership-options.data-v-dcc1a2c8 {
  display: flex;
  gap: 24rpx;
}
.contact-modal .payment-content .membership-tip .membership-options .membership-item.data-v-dcc1a2c8 {
  flex: 1;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}
.contact-modal .payment-content .membership-tip .membership-options .membership-item .price.data-v-dcc1a2c8 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #ff4757;
  margin-bottom: 8rpx;
}
.contact-modal .payment-content .membership-tip .membership-options .membership-item .desc.data-v-dcc1a2c8 {
  display: block;
  font-size: 24rpx;
  color: #666;
}
.contact-modal .payment-content .payment-actions .pay-btn.data-v-dcc1a2c8 {
  width: 100%;
  height: 80rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}
