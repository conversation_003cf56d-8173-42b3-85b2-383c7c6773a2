<view class="job-detail-container data-v-dcc1a2c8"><view class="job-header data-v-dcc1a2c8"><view class="job-info data-v-dcc1a2c8"><text class="job-title data-v-dcc1a2c8">{{jobDetail.title}}</text><text class="salary data-v-dcc1a2c8">{{$root.m0}}</text><view class="job-tags data-v-dcc1a2c8"><block wx:for="{{jobDetail.tags}}" wx:for-item="tag" wx:for-index="__i0__" wx:key="*this"><text class="tag data-v-dcc1a2c8">{{''+tag+''}}</text></block></view></view><view class="company-logo data-v-dcc1a2c8"><image src="{{jobDetail.companyLogo||'/static/default-company.png'}}" mode="aspectFill" class="data-v-dcc1a2c8"></image></view></view><view class="job-requirements data-v-dcc1a2c8"><view class="requirement-item data-v-dcc1a2c8"><text class="iconfont icon-location data-v-dcc1a2c8"></text><text class="data-v-dcc1a2c8">{{jobDetail.location}}</text></view><view class="requirement-item data-v-dcc1a2c8"><text class="iconfont icon-experience data-v-dcc1a2c8"></text><text class="data-v-dcc1a2c8">{{jobDetail.experience}}</text></view><view class="requirement-item data-v-dcc1a2c8"><text class="iconfont icon-education data-v-dcc1a2c8"></text><text class="data-v-dcc1a2c8">{{jobDetail.education}}</text></view><view class="requirement-item data-v-dcc1a2c8"><text class="iconfont icon-time data-v-dcc1a2c8"></text><text class="data-v-dcc1a2c8">{{jobDetail.workType}}</text></view></view><view class="company-info data-v-dcc1a2c8"><view class="company-header data-v-dcc1a2c8"><view class="company-basic data-v-dcc1a2c8"><text class="company-name data-v-dcc1a2c8">{{jobDetail.companyName}}</text><text class="company-scale data-v-dcc1a2c8">{{jobDetail.companyScale}}</text></view><text data-event-opts="{{[['tap',[['viewCompany',['$event']]]]]}}" class="view-company data-v-dcc1a2c8" bindtap="__e">查看详情</text></view><view class="company-desc data-v-dcc1a2c8"><text class="data-v-dcc1a2c8">{{jobDetail.companyDesc}}</text></view></view><view class="job-description data-v-dcc1a2c8"><view class="section-title data-v-dcc1a2c8">职位描述</view><view class="description-content data-v-dcc1a2c8"><rich-text nodes="{{jobDetail.description}}" class="data-v-dcc1a2c8"></rich-text></view></view><block wx:if="{{jobDetail.requirements}}"><view class="job-requirements-detail data-v-dcc1a2c8"><view class="section-title data-v-dcc1a2c8">职位要求</view><view class="requirements-content data-v-dcc1a2c8"><rich-text nodes="{{jobDetail.requirements}}" class="data-v-dcc1a2c8"></rich-text></view></view></block><block wx:if="{{jobDetail.address}}"><view class="work-address data-v-dcc1a2c8"><view class="section-title data-v-dcc1a2c8">工作地址</view><view class="address-info data-v-dcc1a2c8"><view class="address-text data-v-dcc1a2c8"><text class="iconfont icon-location data-v-dcc1a2c8"></text><text class="data-v-dcc1a2c8">{{jobDetail.address}}</text></view><text data-event-opts="{{[['tap',[['viewMap',['$event']]]]]}}" class="view-map data-v-dcc1a2c8" bindtap="__e">查看地图</text></view></view></block><view class="publish-info data-v-dcc1a2c8"><text class="publish-time data-v-dcc1a2c8">{{"发布时间："+$root.m1}}</text><text class="view-count data-v-dcc1a2c8">{{"浏览："+jobDetail.viewCount+"次"}}</text></view><view class="bottom-actions data-v-dcc1a2c8"><button data-event-opts="{{[['tap',[['toggleCollect',['$event']]]]]}}" class="{{['collect-btn','data-v-dcc1a2c8',(isCollected)?'collected':'']}}" bindtap="__e"><text class="{{['iconfont','data-v-dcc1a2c8',isCollected?'icon-heart-fill':'icon-heart']}}"></text><text class="data-v-dcc1a2c8">{{isCollected?'已收藏':'收藏'}}</text></button><button data-event-opts="{{[['tap',[['viewContact',['$event']]]]]}}" class="contact-btn data-v-dcc1a2c8" bindtap="__e"><text class="iconfont icon-phone data-v-dcc1a2c8"></text><text class="data-v-dcc1a2c8">查看联系方式</text></button></view><u-popup bind:input="__e" vue-id="bb71b42c-1" mode="center" width="80%" border-radius="20" value="{{showContactModal}}" data-event-opts="{{[['^input',[['__set_model',['','showContactModal','$event',[]]]]]]}}" class="data-v-dcc1a2c8" bind:__l="__l" vue-slots="{{['default']}}"><view class="contact-modal data-v-dcc1a2c8"><view class="modal-header data-v-dcc1a2c8"><text class="modal-title data-v-dcc1a2c8">联系方式</text><text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="close-btn data-v-dcc1a2c8" bindtap="__e">×</text></view><block wx:if="{{contactInfo.visible}}"><view class="contact-content data-v-dcc1a2c8"><view class="contact-item data-v-dcc1a2c8"><text class="label data-v-dcc1a2c8">联系人：</text><text class="value data-v-dcc1a2c8">{{contactInfo.contactName}}</text></view><view class="contact-item data-v-dcc1a2c8"><text class="label data-v-dcc1a2c8">电话：</text><text data-event-opts="{{[['tap',[['makeCall',['$0'],['contactInfo.phone']]]]]}}" class="value phone data-v-dcc1a2c8" bindtap="__e">{{contactInfo.phone}}</text></view><block wx:if="{{contactInfo.wechat}}"><view class="contact-item data-v-dcc1a2c8"><text class="label data-v-dcc1a2c8">微信：</text><text class="value data-v-dcc1a2c8">{{contactInfo.wechat}}</text></view></block></view></block><block wx:else><view class="payment-content data-v-dcc1a2c8"><view class="payment-info data-v-dcc1a2c8"><text class="payment-title data-v-dcc1a2c8">查看联系方式</text><text class="payment-desc data-v-dcc1a2c8">支付1元即可查看该职位的联系方式</text></view><block wx:if="{{!membershipInfo.isMember}}"><view class="membership-tip data-v-dcc1a2c8"><text class="tip-text data-v-dcc1a2c8">成为会员更划算</text><view class="membership-options data-v-dcc1a2c8"><view data-event-opts="{{[['tap',[['buyMembership',['basic']]]]]}}" class="membership-item data-v-dcc1a2c8" bindtap="__e"><text class="price data-v-dcc1a2c8">¥30</text><text class="desc data-v-dcc1a2c8">查看45个联系人</text></view><view data-event-opts="{{[['tap',[['buyMembership',['premium']]]]]}}" class="membership-item data-v-dcc1a2c8" bindtap="__e"><text class="price data-v-dcc1a2c8">¥50</text><text class="desc data-v-dcc1a2c8">查看80个联系人</text></view></view></view></block><view class="payment-actions data-v-dcc1a2c8"><button class="pay-btn data-v-dcc1a2c8" loading="{{payLoading}}" data-event-opts="{{[['tap',[['payToView',['$event']]]]]}}" bindtap="__e">{{''+(membershipInfo.isMember?'使用会员次数查看':'支付1元查看')+''}}</button></view></view></block></view></u-popup></view>