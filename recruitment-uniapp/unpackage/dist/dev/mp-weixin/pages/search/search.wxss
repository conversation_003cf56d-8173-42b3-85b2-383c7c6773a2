@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.search-container.data-v-4cedc0c6 {
  height: 100vh;
  background: #f8f9fa;
}
.search-container .search-bar.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;
}
.search-container .search-bar .search-input-wrapper.data-v-4cedc0c6 {
  flex: 1;
  display: flex;
  align-items: center;
  height: 72rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 36rpx;
  margin-right: 24rpx;
}
.search-container .search-bar .search-input-wrapper .iconfont.data-v-4cedc0c6 {
  font-size: 32rpx;
  color: #999;
  margin-right: 16rpx;
}
.search-container .search-bar .search-input-wrapper .search-input.data-v-4cedc0c6 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.search-container .search-bar .search-input-wrapper .clear-btn.data-v-4cedc0c6 {
  font-size: 36rpx;
  color: #999;
  line-height: 1;
}
.search-container .search-bar .cancel-btn.data-v-4cedc0c6 {
  font-size: 28rpx;
  color: #007aff;
}
.search-container .suggestions.data-v-4cedc0c6 {
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;
}
.search-container .suggestions .suggestion-item.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f8f9fa;
}
.search-container .suggestions .suggestion-item .iconfont.data-v-4cedc0c6 {
  font-size: 28rpx;
  color: #999;
  margin-right: 16rpx;
}
.search-container .suggestions .suggestion-item .suggestion-text.data-v-4cedc0c6 {
  font-size: 28rpx;
  color: #333;
}
.search-container .hot-keywords.data-v-4cedc0c6,
.search-container .search-history.data-v-4cedc0c6 {
  background: #fff;
  margin-top: 20rpx;
  padding: 32rpx;
}
.search-container .hot-keywords .section-title.data-v-4cedc0c6,
.search-container .search-history .section-title.data-v-4cedc0c6 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}
.search-container .hot-keywords .section-header.data-v-4cedc0c6,
.search-container .search-history .section-header.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.search-container .hot-keywords .section-header .clear-history.data-v-4cedc0c6,
.search-container .search-history .section-header .clear-history.data-v-4cedc0c6 {
  font-size: 24rpx;
  color: #999;
}
.search-container .hot-keywords .keywords-list.data-v-4cedc0c6,
.search-container .search-history .keywords-list.data-v-4cedc0c6 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.search-container .hot-keywords .keywords-list .keyword-item.data-v-4cedc0c6,
.search-container .search-history .keywords-list .keyword-item.data-v-4cedc0c6 {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  color: #666;
  font-size: 26rpx;
  border-radius: 32rpx;
  border: 2rpx solid #e9ecef;
}
.search-container .hot-keywords .history-list .history-item.data-v-4cedc0c6,
.search-container .search-history .history-list .history-item.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
}
.search-container .hot-keywords .history-list .history-item .iconfont.data-v-4cedc0c6,
.search-container .search-history .history-list .history-item .iconfont.data-v-4cedc0c6 {
  font-size: 28rpx;
  color: #999;
  margin-right: 16rpx;
}
.search-container .hot-keywords .history-list .history-item .history-text.data-v-4cedc0c6,
.search-container .search-history .history-list .history-item .history-text.data-v-4cedc0c6 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.search-container .hot-keywords .history-list .history-item .remove-btn.data-v-4cedc0c6,
.search-container .search-history .history-list .history-item .remove-btn.data-v-4cedc0c6 {
  font-size: 32rpx;
  color: #999;
  line-height: 1;
}
.search-container .search-results.data-v-4cedc0c6 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.search-container .search-results .results-header.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;
}
.search-container .search-results .results-header .results-count.data-v-4cedc0c6 {
  font-size: 28rpx;
  color: #666;
}
.search-container .search-results .results-header .sort-btn.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}
.search-container .search-results .results-header .sort-btn .iconfont.data-v-4cedc0c6 {
  font-size: 20rpx;
  color: #999;
  margin-left: 8rpx;
}
.search-container .search-results .results-list.data-v-4cedc0c6 {
  flex: 1;
  padding: 0 32rpx;
}
.search-container .search-results .results-list .job-item.data-v-4cedc0c6 {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 24rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.search-container .search-results .results-list .job-item .job-header.data-v-4cedc0c6 {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.search-container .search-results .results-list .job-item .job-header .job-info.data-v-4cedc0c6 {
  flex: 1;
}
.search-container .search-results .results-list .job-item .job-header .job-info .job-title.data-v-4cedc0c6 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}
.search-container .search-results .results-list .job-item .job-header .job-info .salary.data-v-4cedc0c6 {
  display: block;
  font-size: 28rpx;
  color: #ff4757;
  font-weight: 600;
}
.search-container .search-results .results-list .job-item .job-header .company-logo.data-v-4cedc0c6 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.search-container .search-results .results-list .job-item .job-header .company-logo image.data-v-4cedc0c6 {
  width: 100%;
  height: 100%;
}
.search-container .search-results .results-list .job-item .job-tags.data-v-4cedc0c6 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.search-container .search-results .results-list .job-item .job-tags .tag.data-v-4cedc0c6 {
  padding: 8rpx 16rpx;
  background: #f0f8ff;
  color: #007aff;
  font-size: 24rpx;
  border-radius: 8rpx;
}
.search-container .search-results .results-list .job-item .job-meta.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  gap: 32rpx;
  margin-bottom: 24rpx;
}
.search-container .search-results .results-list .job-item .job-meta .meta-item.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}
.search-container .search-results .results-list .job-item .job-meta .meta-item .iconfont.data-v-4cedc0c6 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.search-container .search-results .results-list .job-item .job-footer.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.search-container .search-results .results-list .job-item .job-footer .company-info .company-name.data-v-4cedc0c6 {
  font-size: 28rpx;
  color: #333;
  margin-right: 16rpx;
}
.search-container .search-results .results-list .job-item .job-footer .company-info .company-scale.data-v-4cedc0c6 {
  font-size: 24rpx;
  color: #666;
}
.search-container .search-results .results-list .job-item .job-footer .publish-time.data-v-4cedc0c6 {
  font-size: 24rpx;
  color: #999;
}
.search-container .search-results .results-list .load-more.data-v-4cedc0c6,
.search-container .search-results .results-list .no-more.data-v-4cedc0c6 {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}
.search-container .search-results .results-list .empty-state.data-v-4cedc0c6 {
  text-align: center;
  padding: 120rpx 0;
}
.search-container .search-results .results-list .empty-state .empty-img.data-v-4cedc0c6 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}
.search-container .search-results .results-list .empty-state .empty-text.data-v-4cedc0c6 {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 16rpx;
}
.search-container .search-results .results-list .empty-state .empty-tip.data-v-4cedc0c6 {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}
.sort-picker .picker-header.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.sort-picker .picker-header .title.data-v-4cedc0c6 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.sort-picker .sort-options .sort-option.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f8f9fa;
  font-size: 28rpx;
  color: #333;
}
.sort-picker .sort-options .sort-option.active.data-v-4cedc0c6 {
  color: #007aff;
  background: #f0f8ff;
}
.sort-picker .sort-options .sort-option .iconfont.data-v-4cedc0c6 {
  font-size: 32rpx;
  color: #007aff;
}
