<view class="search-container data-v-4cedc0c6"><view class="search-bar data-v-4cedc0c6"><view class="search-input-wrapper data-v-4cedc0c6"><text class="iconfont icon-search data-v-4cedc0c6"></text><input class="search-input data-v-4cedc0c6" type="text" placeholder="搜索职位、公司" focus="{{true}}" data-event-opts="{{[['input',[['__set_model',['','keyword','$event',[]]],['onInput',['$event']]]],['confirm',[['onSearch',['$event']]]]]}}" value="{{keyword}}" bindinput="__e" bindconfirm="__e"/><block wx:if="{{keyword}}"><text data-event-opts="{{[['tap',[['clearKeyword',['$event']]]]]}}" class="clear-btn data-v-4cedc0c6" bindtap="__e">×</text></block></view><text data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="cancel-btn data-v-4cedc0c6" bindtap="__e">取消</text></view><block wx:if="{{$root.g0}}"><view class="suggestions data-v-4cedc0c6"><block wx:for="{{suggestions}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view data-event-opts="{{[['tap',[['selectSuggestion',['$0'],[[['suggestions','',__i0__]]]]]]]}}" class="suggestion-item data-v-4cedc0c6" bindtap="__e"><text class="iconfont icon-search data-v-4cedc0c6"></text><text class="suggestion-text data-v-4cedc0c6">{{item}}</text></view></block></view></block><block wx:if="{{$root.g1}}"><view class="hot-keywords data-v-4cedc0c6"><view class="section-title data-v-4cedc0c6">热门搜索</view><view class="keywords-list data-v-4cedc0c6"><block wx:for="{{hotKeywords}}" wx:for-item="item" wx:for-index="__i1__" wx:key="*this"><text data-event-opts="{{[['tap',[['selectKeyword',['$0'],[[['hotKeywords','',__i1__]]]]]]]}}" class="keyword-item data-v-4cedc0c6" bindtap="__e">{{''+item+''}}</text></block></view></view></block><block wx:if="{{$root.g2}}"><view class="search-history data-v-4cedc0c6"><view class="section-header data-v-4cedc0c6"><text class="section-title data-v-4cedc0c6">搜索历史</text><text data-event-opts="{{[['tap',[['clearHistory',['$event']]]]]}}" class="clear-history data-v-4cedc0c6" bindtap="__e">清空</text></view><view class="history-list data-v-4cedc0c6"><block wx:for="{{searchHistory}}" wx:for-item="item" wx:for-index="__i2__" wx:key="*this"><view data-event-opts="{{[['tap',[['selectKeyword',['$0'],[[['searchHistory','',__i2__]]]]]]]}}" class="history-item data-v-4cedc0c6" bindtap="__e"><text class="iconfont icon-time data-v-4cedc0c6"></text><text class="history-text data-v-4cedc0c6">{{item}}</text><text data-event-opts="{{[['tap',[['removeHistory',['$0'],[[['searchHistory','',__i2__]]]]]]]}}" class="remove-btn data-v-4cedc0c6" catchtap="__e">×</text></view></block></view></view></block><block wx:if="{{showResults}}"><view class="search-results data-v-4cedc0c6"><view class="results-header data-v-4cedc0c6"><text class="results-count data-v-4cedc0c6">{{"找到 "+totalCount+" 个相关职位"}}</text><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="sort-btn data-v-4cedc0c6" bindtap="__e"><text class="data-v-4cedc0c6">{{sortText}}</text><text class="iconfont icon-arrow-down data-v-4cedc0c6"></text></view></view><scroll-view class="results-list data-v-4cedc0c6" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><block wx:for="{{$root.l0}}" wx:for-item="job" wx:for-index="__i3__" wx:key="id"><view data-event-opts="{{[['tap',[['goToJobDetail',['$0'],[[['searchResults','id',job.$orig.id,'id']]]]]]]}}" class="job-item data-v-4cedc0c6" bindtap="__e"><view class="job-header data-v-4cedc0c6"><view class="job-info data-v-4cedc0c6"><text class="job-title data-v-4cedc0c6">{{job.$orig.title}}</text><text class="salary data-v-4cedc0c6">{{job.m0}}</text></view><view class="company-logo data-v-4cedc0c6"><image src="{{job.$orig.companyLogo||'/static/default-company.png'}}" mode="aspectFill" class="data-v-4cedc0c6"></image></view></view><view class="job-tags data-v-4cedc0c6"><block wx:for="{{job.$orig.tags}}" wx:for-item="tag" wx:for-index="__i4__" wx:key="*this"><text class="tag data-v-4cedc0c6">{{''+tag+''}}</text></block></view><view class="job-meta data-v-4cedc0c6"><view class="meta-item data-v-4cedc0c6"><text class="iconfont icon-location data-v-4cedc0c6"></text><text class="data-v-4cedc0c6">{{job.$orig.location}}</text></view><view class="meta-item data-v-4cedc0c6"><text class="iconfont icon-experience data-v-4cedc0c6"></text><text class="data-v-4cedc0c6">{{job.$orig.experience}}</text></view><view class="meta-item data-v-4cedc0c6"><text class="iconfont icon-education data-v-4cedc0c6"></text><text class="data-v-4cedc0c6">{{job.$orig.education}}</text></view></view><view class="job-footer data-v-4cedc0c6"><view class="company-info data-v-4cedc0c6"><text class="company-name data-v-4cedc0c6">{{job.$orig.companyName}}</text><text class="company-scale data-v-4cedc0c6">{{job.$orig.companyScale}}</text></view><view class="publish-time data-v-4cedc0c6"><text class="data-v-4cedc0c6">{{job.m1}}</text></view></view></view></block><block wx:if="{{hasMore}}"><view class="load-more data-v-4cedc0c6"><text class="data-v-4cedc0c6">{{loading?'加载中...':'上拉加载更多'}}</text></view></block><block wx:else><block wx:if="{{$root.g3>0}}"><view class="no-more data-v-4cedc0c6"><text class="data-v-4cedc0c6">没有更多数据了</text></view></block></block><block wx:if="{{$root.g4}}"><view class="empty-state data-v-4cedc0c6"><image class="empty-img data-v-4cedc0c6" src="/static/empty-search.png"></image><text class="empty-text data-v-4cedc0c6">没有找到相关职位</text><text class="empty-tip data-v-4cedc0c6">试试其他关键词吧</text></view></block></scroll-view></view></block><u-popup bind:input="__e" vue-id="50cad900-1" mode="bottom" height="40%" border-radius="20" value="{{showSortPicker}}" data-event-opts="{{[['^input',[['__set_model',['','showSortPicker','$event',[]]]]]]}}" class="data-v-4cedc0c6" bind:__l="__l" vue-slots="{{['default']}}"><view class="sort-picker data-v-4cedc0c6"><view class="picker-header data-v-4cedc0c6"><text class="title data-v-4cedc0c6">排序方式</text></view><view class="sort-options data-v-4cedc0c6"><block wx:for="{{sortOptions}}" wx:for-item="option" wx:for-index="__i5__" wx:key="value"><view data-event-opts="{{[['tap',[['selectSort',['$0'],[[['sortOptions','value',option.value,'value']]]]]]]}}" class="{{['sort-option','data-v-4cedc0c6',(sortBy===option.value)?'active':'']}}" bindtap="__e"><text class="data-v-4cedc0c6">{{option.label}}</text><block wx:if="{{sortBy===option.value}}"><text class="iconfont icon-check data-v-4cedc0c6"></text></block></view></block></view></view></u-popup></view>