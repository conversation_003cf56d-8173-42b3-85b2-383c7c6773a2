@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.member-center.data-v-1af0b8c1 {
  min-height: 100vh;
  background: #f8f9fa;
}
.member-center .member-card.data-v-1af0b8c1 {
  position: relative;
  margin: 40rpx;
  border-radius: 24rpx;
  overflow: hidden;
}
.member-center .member-card .card-bg.data-v-1af0b8c1 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.member-center .member-card .card-bg image.data-v-1af0b8c1 {
  width: 100%;
  height: 100%;
}
.member-center .member-card .card-content.data-v-1af0b8c1 {
  position: relative;
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.member-center .member-card .card-content .member-info.data-v-1af0b8c1 {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.member-center .member-card .card-content .member-info .avatar.data-v-1af0b8c1 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  overflow: hidden;
  margin-right: 24rpx;
}
.member-center .member-card .card-content .member-info .avatar image.data-v-1af0b8c1 {
  width: 100%;
  height: 100%;
}
.member-center .member-card .card-content .member-info .user-info .username.data-v-1af0b8c1 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8rpx;
}
.member-center .member-card .card-content .member-info .user-info .member-status.data-v-1af0b8c1 {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.member-center .member-card .card-content .member-benefits.data-v-1af0b8c1 {
  display: flex;
  justify-content: space-between;
}
.member-center .member-card .card-content .member-benefits .benefit-item.data-v-1af0b8c1 {
  text-align: center;
}
.member-center .member-card .card-content .member-benefits .benefit-item .benefit-label.data-v-1af0b8c1 {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}
.member-center .member-card .card-content .member-benefits .benefit-item .benefit-value.data-v-1af0b8c1 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
}
.member-center .member-card .card-content .upgrade-tip.data-v-1af0b8c1 {
  text-align: center;
}
.member-center .member-card .card-content .upgrade-tip .tip-text.data-v-1af0b8c1 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}
.member-center .membership-packages.data-v-1af0b8c1 {
  margin: 40rpx;
}
.member-center .membership-packages .section-title.data-v-1af0b8c1 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}
.member-center .membership-packages .packages-list.data-v-1af0b8c1 {
  display: flex;
  gap: 24rpx;
}
.member-center .membership-packages .packages-list .package-item.data-v-1af0b8c1 {
  flex: 1;
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  border: 2rpx solid #e9ecef;
}
.member-center .membership-packages .packages-list .package-item.recommended.data-v-1af0b8c1 {
  border-color: #007aff;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.15);
}
.member-center .membership-packages .packages-list .package-item .recommended-tag.data-v-1af0b8c1 {
  position: absolute;
  top: -2rpx;
  right: 32rpx;
  background: #007aff;
  color: #fff;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 0 0 12rpx 12rpx;
}
.member-center .membership-packages .packages-list .package-item .package-header.data-v-1af0b8c1 {
  text-align: center;
  margin-bottom: 32rpx;
}
.member-center .membership-packages .packages-list .package-item .package-header .package-name.data-v-1af0b8c1 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.member-center .membership-packages .packages-list .package-item .package-header .package-price .price.data-v-1af0b8c1 {
  font-size: 48rpx;
  font-weight: 600;
  color: #ff4757;
}
.member-center .membership-packages .packages-list .package-item .package-header .package-price .original-price.data-v-1af0b8c1 {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 16rpx;
}
.member-center .membership-packages .packages-list .package-item .package-benefits.data-v-1af0b8c1 {
  margin-bottom: 32rpx;
}
.member-center .membership-packages .packages-list .package-item .package-benefits .benefit-item.data-v-1af0b8c1 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.member-center .membership-packages .packages-list .package-item .package-benefits .benefit-item .iconfont.data-v-1af0b8c1 {
  font-size: 24rpx;
  color: #07c160;
  margin-right: 12rpx;
}
.member-center .membership-packages .packages-list .package-item .package-benefits .benefit-item .benefit-text.data-v-1af0b8c1 {
  font-size: 24rpx;
  color: #666;
}
.member-center .membership-packages .packages-list .package-item .buy-btn.data-v-1af0b8c1 {
  width: 100%;
  height: 72rpx;
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
  border-radius: 36rpx;
  font-size: 28rpx;
}
.member-center .membership-packages .packages-list .package-item .buy-btn.primary.data-v-1af0b8c1 {
  background: #007aff;
  color: #fff;
  border-color: #007aff;
}
.member-center .function-entries.data-v-1af0b8c1 {
  margin: 40rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}
.member-center .function-entries .entry-item.data-v-1af0b8c1 {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f8f9fa;
}
.member-center .function-entries .entry-item.data-v-1af0b8c1:last-child {
  border-bottom: none;
}
.member-center .function-entries .entry-item .entry-icon.data-v-1af0b8c1 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f8ff;
  border-radius: 40rpx;
  margin-right: 24rpx;
}
.member-center .function-entries .entry-item .entry-icon .iconfont.data-v-1af0b8c1 {
  font-size: 36rpx;
  color: #007aff;
}
.member-center .function-entries .entry-item .entry-info.data-v-1af0b8c1 {
  flex: 1;
}
.member-center .function-entries .entry-item .entry-info .entry-title.data-v-1af0b8c1 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.member-center .function-entries .entry-item .entry-info .entry-desc.data-v-1af0b8c1 {
  display: block;
  font-size: 24rpx;
  color: #999;
}
.member-center .function-entries .entry-item .icon-arrow-right.data-v-1af0b8c1 {
  font-size: 24rpx;
  color: #ccc;
}
.payment-modal.data-v-1af0b8c1 {
  padding: 40rpx;
}
.payment-modal .modal-header.data-v-1af0b8c1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.payment-modal .modal-header .modal-title.data-v-1af0b8c1 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.payment-modal .modal-header .close-btn.data-v-1af0b8c1 {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}
.payment-modal .payment-info.data-v-1af0b8c1 {
  margin-bottom: 40rpx;
}
.payment-modal .payment-info .package-info.data-v-1af0b8c1 {
  text-align: center;
  margin-bottom: 24rpx;
}
.payment-modal .payment-info .package-info .package-name.data-v-1af0b8c1 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.payment-modal .payment-info .package-info .package-desc.data-v-1af0b8c1 {
  display: block;
  font-size: 24rpx;
  color: #666;
}
.payment-modal .payment-info .price-info.data-v-1af0b8c1 {
  text-align: center;
}
.payment-modal .payment-info .price-info .price.data-v-1af0b8c1 {
  font-size: 48rpx;
  font-weight: 600;
  color: #ff4757;
}
.payment-modal .payment-info .price-info .original-price.data-v-1af0b8c1 {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 16rpx;
}
.payment-modal .payment-methods.data-v-1af0b8c1 {
  margin-bottom: 40rpx;
}
.payment-modal .payment-methods .method-title.data-v-1af0b8c1 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 24rpx;
}
.payment-modal .payment-methods .method-item.data-v-1af0b8c1 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}
.payment-modal .payment-methods .method-item.active.data-v-1af0b8c1 {
  border-color: #007aff;
  background: #f0f8ff;
}
.payment-modal .payment-methods .method-item .iconfont.data-v-1af0b8c1 {
  font-size: 32rpx;
  color: #07c160;
  margin-right: 16rpx;
}
.payment-modal .payment-methods .method-item .method-name.data-v-1af0b8c1 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.payment-modal .payment-methods .method-item .icon-check.data-v-1af0b8c1 {
  font-size: 24rpx;
  color: #007aff;
}
.payment-modal .confirm-pay-btn.data-v-1af0b8c1 {
  width: 100%;
  height: 88rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}
