<view class="member-center data-v-1af0b8c1"><view class="member-card data-v-1af0b8c1"><view class="card-bg data-v-1af0b8c1"><image src="/static/member-bg.png" mode="aspectFill" class="data-v-1af0b8c1"></image></view><view class="card-content data-v-1af0b8c1"><view class="member-info data-v-1af0b8c1"><view class="avatar data-v-1af0b8c1"><image src="{{userInfo.avatar||'/static/default-avatar.png'}}" mode="aspectFill" class="data-v-1af0b8c1"></image></view><view class="user-info data-v-1af0b8c1"><text class="username data-v-1af0b8c1">{{userInfo.name||'用户'}}</text><text class="member-status data-v-1af0b8c1">{{membershipInfo.isMember?'会员用户':'普通用户'}}</text></view></view><block wx:if="{{membershipInfo.isMember}}"><view class="member-benefits data-v-1af0b8c1"><view class="benefit-item data-v-1af0b8c1"><text class="benefit-label data-v-1af0b8c1">剩余次数</text><text class="benefit-value data-v-1af0b8c1">{{membershipInfo.remainingViews+"次"}}</text></view><view class="benefit-item data-v-1af0b8c1"><text class="benefit-label data-v-1af0b8c1">到期时间</text><text class="benefit-value data-v-1af0b8c1">{{$root.m0}}</text></view></view></block><block wx:else><view class="upgrade-tip data-v-1af0b8c1"><text class="tip-text data-v-1af0b8c1">开通会员，享受更多特权</text></view></block></view></view><view class="membership-packages data-v-1af0b8c1"><view class="section-title data-v-1af0b8c1">会员套餐</view><view class="packages-list data-v-1af0b8c1"><block wx:for="{{packages}}" wx:for-item="pkg" wx:for-index="__i0__" wx:key="type"><view data-event-opts="{{[['tap',[['selectPackage',['$0'],[[['packages','type',pkg.type]]]]]]]}}" class="{{['package-item','data-v-1af0b8c1',(pkg.recommended)?'recommended':'']}}" bindtap="__e"><block wx:if="{{pkg.recommended}}"><view class="recommended-tag data-v-1af0b8c1">推荐</view></block><view class="package-header data-v-1af0b8c1"><text class="package-name data-v-1af0b8c1">{{pkg.name}}</text><view class="package-price data-v-1af0b8c1"><text class="price data-v-1af0b8c1">{{"¥"+pkg.price}}</text><block wx:if="{{pkg.originalPrice}}"><text class="original-price data-v-1af0b8c1">{{"¥"+pkg.originalPrice}}</text></block></view></view><view class="package-benefits data-v-1af0b8c1"><block wx:for="{{pkg.benefits}}" wx:for-item="benefit" wx:for-index="__i1__" wx:key="*this"><view class="benefit-item data-v-1af0b8c1"><text class="iconfont icon-check data-v-1af0b8c1"></text><text class="benefit-text data-v-1af0b8c1">{{benefit}}</text></view></block></view><button class="{{['buy-btn','data-v-1af0b8c1',(pkg.recommended)?'primary':'']}}" loading="{{buyLoading===pkg.type}}" data-event-opts="{{[['tap',[['buyPackage',['$0'],[[['packages','type',pkg.type]]]]]]]}}" catchtap="__e">{{''+(membershipInfo.isMember?'续费':'立即开通')+''}}</button></view></block></view></view><view class="function-entries data-v-1af0b8c1"><view data-event-opts="{{[['tap',[['goToConsumptionRecords',['$event']]]]]}}" class="entry-item data-v-1af0b8c1" bindtap="__e"><view class="entry-icon data-v-1af0b8c1"><text class="iconfont icon-bill data-v-1af0b8c1"></text></view><view class="entry-info data-v-1af0b8c1"><text class="entry-title data-v-1af0b8c1">消费记录</text><text class="entry-desc data-v-1af0b8c1">查看付费记录和剩余次数</text></view><text class="iconfont icon-arrow-right data-v-1af0b8c1"></text></view><view data-event-opts="{{[['tap',[['goToMemberRights',['$event']]]]]}}" class="entry-item data-v-1af0b8c1" bindtap="__e"><view class="entry-icon data-v-1af0b8c1"><text class="iconfont icon-vip data-v-1af0b8c1"></text></view><view class="entry-info data-v-1af0b8c1"><text class="entry-title data-v-1af0b8c1">会员权益</text><text class="entry-desc data-v-1af0b8c1">了解会员专享特权</text></view><text class="iconfont icon-arrow-right data-v-1af0b8c1"></text></view><view data-event-opts="{{[['tap',[['contactService',['$event']]]]]}}" class="entry-item data-v-1af0b8c1" bindtap="__e"><view class="entry-icon data-v-1af0b8c1"><text class="iconfont icon-service data-v-1af0b8c1"></text></view><view class="entry-info data-v-1af0b8c1"><text class="entry-title data-v-1af0b8c1">客服咨询</text><text class="entry-desc data-v-1af0b8c1">遇到问题？联系客服</text></view><text class="iconfont icon-arrow-right data-v-1af0b8c1"></text></view></view><u-popup bind:input="__e" vue-id="4326e065-1" mode="center" width="80%" border-radius="20" value="{{showPaymentModal}}" data-event-opts="{{[['^input',[['__set_model',['','showPaymentModal','$event',[]]]]]]}}" class="data-v-1af0b8c1" bind:__l="__l" vue-slots="{{['default']}}"><view class="payment-modal data-v-1af0b8c1"><view class="modal-header data-v-1af0b8c1"><text class="modal-title data-v-1af0b8c1">确认支付</text><text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="close-btn data-v-1af0b8c1" bindtap="__e">×</text></view><view class="payment-info data-v-1af0b8c1"><view class="package-info data-v-1af0b8c1"><text class="package-name data-v-1af0b8c1">{{selectedPackage.name}}</text><text class="package-desc data-v-1af0b8c1">{{selectedPackage.desc}}</text></view><view class="price-info data-v-1af0b8c1"><text class="price data-v-1af0b8c1">{{"¥"+selectedPackage.price}}</text><block wx:if="{{selectedPackage.originalPrice}}"><text class="original-price data-v-1af0b8c1">{{"¥"+selectedPackage.originalPrice}}</text></block></view></view><view class="payment-methods data-v-1af0b8c1"><view class="method-title data-v-1af0b8c1">支付方式</view><view class="method-item active data-v-1af0b8c1"><text class="iconfont icon-wechat-pay data-v-1af0b8c1"></text><text class="method-name data-v-1af0b8c1">微信支付</text><text class="iconfont icon-check data-v-1af0b8c1"></text></view></view><button class="confirm-pay-btn data-v-1af0b8c1" loading="{{payLoading}}" data-event-opts="{{[['tap',[['confirmPayment',['$event']]]]]}}" bindtap="__e">{{'确认支付 ¥'+selectedPackage.price+''}}</button></view></u-popup></view>