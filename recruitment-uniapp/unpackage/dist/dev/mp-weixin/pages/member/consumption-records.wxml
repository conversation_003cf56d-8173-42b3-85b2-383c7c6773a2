<view class="consumption-records data-v-04a390d4"><view class="stats-card data-v-04a390d4"><view class="stats-item data-v-04a390d4"><text class="stats-value data-v-04a390d4">{{membershipInfo.remainingViews||0}}</text><text class="stats-label data-v-04a390d4">剩余次数</text></view><view class="stats-divider data-v-04a390d4"></view><view class="stats-item data-v-04a390d4"><text class="stats-value data-v-04a390d4">{{totalConsumption}}</text><text class="stats-label data-v-04a390d4">累计消费</text></view><view class="stats-divider data-v-04a390d4"></view><view class="stats-item data-v-04a390d4"><text class="stats-value data-v-04a390d4">{{usedViews}}</text><text class="stats-label data-v-04a390d4">已使用次数</text></view></view><view class="filter-bar data-v-04a390d4"><view class="filter-tabs data-v-04a390d4"><block wx:for="{{filterTabs}}" wx:for-item="tab" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['switchFilter',['$0'],[[['filterTabs','value',tab.value,'value']]]]]]]}}" class="{{['filter-tab','data-v-04a390d4',(activeFilter===tab.value)?'active':'']}}" bindtap="__e">{{''+tab.label+''}}</view></block></view></view><scroll-view class="records-list data-v-04a390d4" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><block wx:for="{{$root.l0}}" wx:for-item="record" wx:for-index="__i1__" wx:key="id"><view class="record-item data-v-04a390d4"><view class="record-icon data-v-04a390d4"><text class="{{['iconfont','data-v-04a390d4',(record.$orig.type==='recharge')?'icon-recharge':'',(record.$orig.type==='consume')?'icon-consume':'',(record.$orig.type==='refund')?'icon-refund':'',(record.$orig.type==='membership')?'icon-vip':'',(!record.g0)?'icon-bill':'']}}"></text></view><view class="record-info data-v-04a390d4"><text class="record-title data-v-04a390d4">{{record.$orig.title}}</text><text class="record-desc data-v-04a390d4">{{record.$orig.description}}</text><text class="record-time data-v-04a390d4">{{record.m0}}</text></view><view class="record-amount data-v-04a390d4"><text class="{{['amount','data-v-04a390d4',(record.$orig.type==='recharge'||record.$orig.type==='refund')?'positive':'',(record.$orig.type==='consume'||record.$orig.type==='membership')?'negative':'']}}">{{''+record.m1+''}}</text><text class="{{['status','data-v-04a390d4',(record.$orig.status==='success')?'success':'',(record.$orig.status==='pending')?'pending':'',(record.$orig.status==='failed')?'failed':'',(record.$orig.status==='refunded')?'refunded':'']}}">{{''+record.m2+''}}</text></view></view></block><block wx:if="{{hasMore}}"><view class="load-more data-v-04a390d4"><text class="data-v-04a390d4">{{loading?'加载中...':'上拉加载更多'}}</text></view></block><block wx:else><block wx:if="{{$root.g1>0}}"><view class="no-more data-v-04a390d4"><text class="data-v-04a390d4">没有更多记录了</text></view></block></block><block wx:if="{{$root.g2}}"><view class="empty-state data-v-04a390d4"><image class="empty-img data-v-04a390d4" src="/static/empty-records.png"></image><text class="empty-text data-v-04a390d4">暂无消费记录</text><button data-event-opts="{{[['tap',[['goToMemberCenter',['$event']]]]]}}" class="go-member-btn data-v-04a390d4" bindtap="__e">开通会员</button></view></block></scroll-view></view>