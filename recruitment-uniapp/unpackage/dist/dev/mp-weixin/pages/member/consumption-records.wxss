@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.consumption-records.data-v-04a390d4 {
  min-height: 100vh;
  background: #f8f9fa;
}
.consumption-records .stats-card.data-v-04a390d4 {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 40rpx;
  padding: 40rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.consumption-records .stats-card .stats-item.data-v-04a390d4 {
  flex: 1;
  text-align: center;
}
.consumption-records .stats-card .stats-item .stats-value.data-v-04a390d4 {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.consumption-records .stats-card .stats-item .stats-label.data-v-04a390d4 {
  display: block;
  font-size: 24rpx;
  color: #999;
}
.consumption-records .stats-card .stats-divider.data-v-04a390d4 {
  width: 2rpx;
  height: 80rpx;
  background: #f0f0f0;
  margin: 0 40rpx;
}
.consumption-records .filter-bar.data-v-04a390d4 {
  background: #fff;
  padding: 0 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.consumption-records .filter-bar .filter-tabs.data-v-04a390d4 {
  display: flex;
}
.consumption-records .filter-bar .filter-tabs .filter-tab.data-v-04a390d4 {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.consumption-records .filter-bar .filter-tabs .filter-tab.active.data-v-04a390d4 {
  color: #007aff;
  font-weight: 500;
}
.consumption-records .filter-bar .filter-tabs .filter-tab.active.data-v-04a390d4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #007aff;
  border-radius: 2rpx;
}
.consumption-records .records-list.data-v-04a390d4 {
  flex: 1;
  padding: 0 40rpx;
}
.consumption-records .records-list .record-item.data-v-04a390d4 {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 32rpx;
  margin: 24rpx 0;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.consumption-records .records-list .record-item .record-icon.data-v-04a390d4 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f8ff;
  border-radius: 40rpx;
  margin-right: 24rpx;
}
.consumption-records .records-list .record-item .record-icon .iconfont.data-v-04a390d4 {
  font-size: 36rpx;
  color: #007aff;
}
.consumption-records .records-list .record-item .record-info.data-v-04a390d4 {
  flex: 1;
}
.consumption-records .records-list .record-item .record-info .record-title.data-v-04a390d4 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.consumption-records .records-list .record-item .record-info .record-desc.data-v-04a390d4 {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.consumption-records .records-list .record-item .record-info .record-time.data-v-04a390d4 {
  display: block;
  font-size: 22rpx;
  color: #999;
}
.consumption-records .records-list .record-item .record-amount.data-v-04a390d4 {
  text-align: right;
}
.consumption-records .records-list .record-item .record-amount .amount.data-v-04a390d4 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.consumption-records .records-list .record-item .record-amount .amount.positive.data-v-04a390d4 {
  color: #07c160;
}
.consumption-records .records-list .record-item .record-amount .amount.negative.data-v-04a390d4 {
  color: #ff4757;
}
.consumption-records .records-list .record-item .record-amount .status.data-v-04a390d4 {
  display: block;
  font-size: 22rpx;
}
.consumption-records .records-list .record-item .record-amount .status.success.data-v-04a390d4 {
  color: #07c160;
}
.consumption-records .records-list .record-item .record-amount .status.pending.data-v-04a390d4 {
  color: #ffa500;
}
.consumption-records .records-list .record-item .record-amount .status.failed.data-v-04a390d4 {
  color: #ff4757;
}
.consumption-records .records-list .record-item .record-amount .status.refunded.data-v-04a390d4 {
  color: #999;
}
.consumption-records .records-list .load-more.data-v-04a390d4,
.consumption-records .records-list .no-more.data-v-04a390d4 {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}
.consumption-records .records-list .empty-state.data-v-04a390d4 {
  text-align: center;
  padding: 120rpx 0;
}
.consumption-records .records-list .empty-state .empty-img.data-v-04a390d4 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}
.consumption-records .records-list .empty-state .empty-text.data-v-04a390d4 {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.consumption-records .records-list .empty-state .go-member-btn.data-v-04a390d4 {
  width: 200rpx;
  height: 64rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 32rpx;
  font-size: 28rpx;
}
