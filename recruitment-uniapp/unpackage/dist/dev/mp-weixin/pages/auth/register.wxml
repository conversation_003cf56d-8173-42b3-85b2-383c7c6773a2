<view class="register-container data-v-6e828681"><view class="header data-v-6e828681"><text class="title data-v-6e828681">用户注册</text><text class="subtitle data-v-6e828681">完善个人信息，开启求职之旅</text></view><view class="form data-v-6e828681"><view class="form-item avatar-item data-v-6e828681"><text class="label data-v-6e828681">头像</text><view data-event-opts="{{[['tap',[['chooseAvatar',['$event']]]]]}}" class="avatar-upload data-v-6e828681" bindtap="__e"><block wx:if="{{form.avatar}}"><image class="avatar-img data-v-6e828681" src="{{form.avatar}}" mode="aspectFill"></image></block><block wx:else><view class="avatar-placeholder data-v-6e828681"><text class="iconfont icon-camera data-v-6e828681"></text><text class="placeholder-text data-v-6e828681">上传头像</text></view></block></view></view><view class="form-item data-v-6e828681"><text class="label data-v-6e828681">姓名<text class="required data-v-6e828681">*</text></text><input class="input data-v-6e828681" type="text" placeholder="请输入真实姓名" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['form']]]]]}}" value="{{form.name}}" bindinput="__e"/></view><view class="form-item data-v-6e828681"><text class="label data-v-6e828681">手机号<text class="required data-v-6e828681">*</text></text><input class="input data-v-6e828681" type="number" placeholder="请输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['form']]]]]}}" value="{{form.phone}}" bindinput="__e"/></view><view class="form-item data-v-6e828681"><text class="label data-v-6e828681">验证码<text class="required data-v-6e828681">*</text></text><view class="code-input data-v-6e828681"><input class="input data-v-6e828681" type="number" placeholder="请输入验证码" maxlength="6" data-event-opts="{{[['input',[['__set_model',['$0','code','$event',[]],['form']]]]]}}" value="{{form.code}}" bindinput="__e"/><button class="code-btn data-v-6e828681" disabled="{{codeDisabled}}" data-event-opts="{{[['tap',[['sendCode',['$event']]]]]}}" bindtap="__e">{{''+codeText+''}}</button></view></view><view class="form-item data-v-6e828681"><text class="label data-v-6e828681">性别</text><radio-group data-event-opts="{{[['change',[['onGenderChange',['$event']]]]]}}" class="radio-group data-v-6e828681" bindchange="__e"><label class="radio-item data-v-6e828681"><radio value="1" checked="{{form.gender==='1'}}" color="#007aff" class="data-v-6e828681"></radio><text class="data-v-6e828681">男</text></label><label class="radio-item data-v-6e828681"><radio value="2" checked="{{form.gender==='2'}}" color="#007aff" class="data-v-6e828681"></radio><text class="data-v-6e828681">女</text></label></radio-group></view><view class="form-item data-v-6e828681"><text class="label data-v-6e828681">年龄</text><input class="input data-v-6e828681" type="number" placeholder="请输入年龄" maxlength="2" data-event-opts="{{[['input',[['__set_model',['$0','age','$event',[]],['form']]]]]}}" value="{{form.age}}" bindinput="__e"/></view><view class="form-item data-v-6e828681"><text class="label data-v-6e828681">所在地区</text><region-picker bind:input="__e" vue-id="61cecaa5-1" value="{{form.region}}" data-event-opts="{{[['^input',[['__set_model',['$0','region','$event',[]],['form']]]]]}}" class="data-v-6e828681" bind:__l="__l"></region-picker></view><view class="form-item data-v-6e828681"><text class="label data-v-6e828681">期望职位</text><input class="input data-v-6e828681" type="text" placeholder="请输入期望职位" data-event-opts="{{[['input',[['__set_model',['$0','expectedJob','$event',[]],['form']]]]]}}" value="{{form.expectedJob}}" bindinput="__e"/></view><view class="form-item data-v-6e828681"><text class="label data-v-6e828681">期望薪资</text><view class="salary-input data-v-6e828681"><input class="input salary-min data-v-6e828681" type="number" placeholder="最低" data-event-opts="{{[['input',[['__set_model',['$0','minSalary','$event',[]],['form']]]]]}}" value="{{form.minSalary}}" bindinput="__e"/><text class="separator data-v-6e828681">-</text><input class="input salary-max data-v-6e828681" type="number" placeholder="最高" data-event-opts="{{[['input',[['__set_model',['$0','maxSalary','$event',[]],['form']]]]]}}" value="{{form.maxSalary}}" bindinput="__e"/><text class="unit data-v-6e828681">元/月</text></view></view><view class="form-item data-v-6e828681"><text class="label data-v-6e828681">工作经验</text><picker range="{{experienceOptions}}" value="{{experienceIndex}}" data-event-opts="{{[['change',[['onExperienceChange',['$event']]]]]}}" bindchange="__e" class="data-v-6e828681"><view class="picker-input data-v-6e828681"><text class="picker-text data-v-6e828681">{{experienceOptions[experienceIndex]||'请选择工作经验'}}</text><text class="iconfont icon-arrow-down data-v-6e828681"></text></view></picker></view><view class="form-item data-v-6e828681"><text class="label data-v-6e828681">学历</text><picker range="{{educationOptions}}" value="{{educationIndex}}" data-event-opts="{{[['change',[['onEducationChange',['$event']]]]]}}" bindchange="__e" class="data-v-6e828681"><view class="picker-input data-v-6e828681"><text class="picker-text data-v-6e828681">{{educationOptions[educationIndex]||'请选择学历'}}</text><text class="iconfont icon-arrow-down data-v-6e828681"></text></view></picker></view><view class="form-item data-v-6e828681"><text class="label data-v-6e828681">个人简介</text><textarea class="textarea data-v-6e828681" placeholder="请简单介绍一下自己的工作经历和技能特长" maxlength="500" data-event-opts="{{[['input',[['__set_model',['$0','introduction','$event',[]],['form']]]]]}}" value="{{form.introduction}}" bindinput="__e"></textarea><text class="char-count data-v-6e828681">{{$root.g0+"/500"}}</text></view></view><view class="agreement data-v-6e828681"><checkbox-group data-event-opts="{{[['change',[['onAgreementChange',['$event']]]]]}}" bindchange="__e" class="data-v-6e828681"><checkbox checked="{{agreed}}" color="#007aff" class="data-v-6e828681"></checkbox></checkbox-group><text class="agreement-text data-v-6e828681">我已阅读并同意<text data-event-opts="{{[['tap',[['showAgreement',['user']]]]]}}" class="link data-v-6e828681" bindtap="__e">《用户协议》</text>和<text data-event-opts="{{[['tap',[['showAgreement',['privacy']]]]]}}" class="link data-v-6e828681" bindtap="__e">《隐私政策》</text></text></view><button class="register-btn data-v-6e828681" disabled="{{!canSubmit}}" loading="{{loading}}" data-event-opts="{{[['tap',[['handleRegister',['$event']]]]]}}" bindtap="__e">立即注册</button></view>