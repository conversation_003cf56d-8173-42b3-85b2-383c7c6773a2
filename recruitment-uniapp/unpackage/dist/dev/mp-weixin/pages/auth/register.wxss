@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.register-container.data-v-6e828681 {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 40rpx;
}
.register-container .header.data-v-6e828681 {
  text-align: center;
  margin-bottom: 60rpx;
}
.register-container .header .title.data-v-6e828681 {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.register-container .header .subtitle.data-v-6e828681 {
  display: block;
  font-size: 28rpx;
  color: #666;
}
.register-container .form.data-v-6e828681 {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}
.register-container .form .form-item.data-v-6e828681 {
  margin-bottom: 40rpx;
}
.register-container .form .form-item.data-v-6e828681:last-child {
  margin-bottom: 0;
}
.register-container .form .form-item .label.data-v-6e828681 {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}
.register-container .form .form-item .label .required.data-v-6e828681 {
  color: #ff4757;
}
.register-container .form .form-item .input.data-v-6e828681 {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f8f9fa;
}
.register-container .form .form-item .textarea.data-v-6e828681 {
  width: 100%;
  min-height: 160rpx;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f8f9fa;
  resize: none;
}
.register-container .form .form-item .char-count.data-v-6e828681 {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}
.register-container .form .form-item.avatar-item .avatar-upload.data-v-6e828681 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 2rpx solid #e9ecef;
  overflow: hidden;
}
.register-container .form .form-item.avatar-item .avatar-upload .avatar-img.data-v-6e828681 {
  width: 100%;
  height: 100%;
}
.register-container .form .form-item.avatar-item .avatar-upload .avatar-placeholder.data-v-6e828681 {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}
.register-container .form .form-item.avatar-item .avatar-upload .avatar-placeholder .iconfont.data-v-6e828681 {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 8rpx;
}
.register-container .form .form-item.avatar-item .avatar-upload .avatar-placeholder .placeholder-text.data-v-6e828681 {
  font-size: 20rpx;
  color: #999;
}
.register-container .form .form-item .code-input.data-v-6e828681 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.register-container .form .form-item .code-input .input.data-v-6e828681 {
  flex: 1;
}
.register-container .form .form-item .code-input .code-btn.data-v-6e828681 {
  width: 200rpx;
  height: 80rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
}
.register-container .form .form-item .code-input .code-btn.data-v-6e828681:disabled {
  background: #ccc;
}
.register-container .form .form-item .radio-group.data-v-6e828681 {
  display: flex;
  gap: 40rpx;
}
.register-container .form .form-item .radio-group .radio-item.data-v-6e828681 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #333;
}
.register-container .form .form-item .salary-input.data-v-6e828681 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.register-container .form .form-item .salary-input .salary-min.data-v-6e828681,
.register-container .form .form-item .salary-input .salary-max.data-v-6e828681 {
  flex: 1;
}
.register-container .form .form-item .salary-input .separator.data-v-6e828681 {
  font-size: 28rpx;
  color: #666;
}
.register-container .form .form-item .salary-input .unit.data-v-6e828681 {
  font-size: 28rpx;
  color: #666;
}
.register-container .form .form-item .picker-input.data-v-6e828681 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  background: #f8f9fa;
}
.register-container .form .form-item .picker-input .picker-text.data-v-6e828681 {
  font-size: 28rpx;
  color: #333;
}
.register-container .form .form-item .picker-input .iconfont.data-v-6e828681 {
  font-size: 24rpx;
  color: #999;
}
.register-container .agreement.data-v-6e828681 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
  font-size: 24rpx;
  color: #666;
}
.register-container .agreement checkbox-group.data-v-6e828681 {
  margin-right: 16rpx;
  margin-top: 4rpx;
}
.register-container .agreement .agreement-text.data-v-6e828681 {
  flex: 1;
  line-height: 1.5;
}
.register-container .agreement .agreement-text .link.data-v-6e828681 {
  color: #007aff;
  text-decoration: underline;
}
.register-container .register-btn.data-v-6e828681 {
  width: 100%;
  height: 88rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}
.register-container .register-btn.data-v-6e828681:disabled {
  background: #ccc;
}
