<view class="login-container data-v-cbd6070a"><view class="header data-v-cbd6070a"><image class="logo data-v-cbd6070a" src="/static/logo.png" mode="aspectFit"></image><text class="app-name data-v-cbd6070a">招聘平台</text><text class="slogan data-v-cbd6070a">找工作，就上招聘平台</text></view><view class="login-methods data-v-cbd6070a"><button class="login-btn wechat-btn data-v-cbd6070a" open-type="getUserInfo" loading="{{wechatLoading}}" data-event-opts="{{[['getuserinfo',[['handleWechatLogin',['$event']]]]]}}" bindgetuserinfo="__e"><text class="iconfont icon-wechat data-v-cbd6070a"></text><text class="data-v-cbd6070a">微信一键登录</text></button><button class="login-btn phone-btn data-v-cbd6070a" open-type="getPhoneNumber" loading="{{phoneLoading}}" data-event-opts="{{[['getphonenumber',[['handlePhoneLogin',['$event']]]]]}}" bindgetphonenumber="__e"><text class="iconfont icon-phone data-v-cbd6070a"></text><text class="data-v-cbd6070a">手机号一键登录</text></button><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="manual-phone data-v-cbd6070a" bindtap="__e"><text class="iconfont icon-mobile data-v-cbd6070a"></text><text class="data-v-cbd6070a">手动输入手机号登录</text></view></view><view class="agreement data-v-cbd6070a"><checkbox-group data-event-opts="{{[['change',[['onAgreementChange',['$event']]]]]}}" bindchange="__e" class="data-v-cbd6070a"><checkbox checked="{{agreed}}" color="#007aff" class="data-v-cbd6070a"></checkbox></checkbox-group><text class="agreement-text data-v-cbd6070a">我已阅读并同意<text data-event-opts="{{[['tap',[['showAgreement',['user']]]]]}}" class="link data-v-cbd6070a" bindtap="__e">《用户协议》</text>和<text data-event-opts="{{[['tap',[['showAgreement',['privacy']]]]]}}" class="link data-v-cbd6070a" bindtap="__e">《隐私政策》</text></text></view><u-popup bind:input="__e" vue-id="aee9d82a-1" mode="center" width="80%" border-radius="20" value="{{showManualLogin}}" data-event-opts="{{[['^input',[['__set_model',['','showManualLogin','$event',[]]]]]]}}" class="data-v-cbd6070a" bind:__l="__l" vue-slots="{{['default']}}"><view class="manual-login-popup data-v-cbd6070a"><view class="popup-header data-v-cbd6070a"><text class="popup-title data-v-cbd6070a">手机号登录</text><text data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="close-btn data-v-cbd6070a" bindtap="__e">×</text></view><view class="form data-v-cbd6070a"><view class="form-item data-v-cbd6070a"><text class="label data-v-cbd6070a">手机号</text><input class="input data-v-cbd6070a" type="number" placeholder="请输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['phoneForm']]]]]}}" value="{{phoneForm.phone}}" bindinput="__e"/></view><view class="form-item data-v-cbd6070a"><text class="label data-v-cbd6070a">验证码</text><view class="code-input data-v-cbd6070a"><input class="input data-v-cbd6070a" type="number" placeholder="请输入验证码" maxlength="6" data-event-opts="{{[['input',[['__set_model',['$0','code','$event',[]],['phoneForm']]]]]}}" value="{{phoneForm.code}}" bindinput="__e"/><button class="code-btn data-v-cbd6070a" disabled="{{codeDisabled}}" data-event-opts="{{[['tap',[['sendCode',['$event']]]]]}}" bindtap="__e">{{''+codeText+''}}</button></view></view><button class="submit-btn data-v-cbd6070a" disabled="{{!canSubmit}}" loading="{{manualLoading}}" data-event-opts="{{[['tap',[['handleManualLogin',['$event']]]]]}}" bindtap="__e">登录</button></view></view></u-popup></view>