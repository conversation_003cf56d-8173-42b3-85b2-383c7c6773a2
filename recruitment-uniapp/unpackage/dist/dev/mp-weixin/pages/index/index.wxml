<view class="job-list-container data-v-57280228"><view class="search-bar data-v-57280228"><view data-event-opts="{{[['tap',[['goToSearch',['$event']]]]]}}" class="search-input data-v-57280228" bindtap="__e"><text class="iconfont icon-search data-v-57280228"></text><text class="placeholder data-v-57280228">搜索职位、公司</text></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="location-btn data-v-57280228" bindtap="__e"><text class="location-text data-v-57280228">{{currentLocation}}</text><text class="iconfont icon-arrow-down data-v-57280228"></text></view></view><view class="filter-bar data-v-57280228"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="filter-item data-v-57280228" bindtap="__e"><text class="data-v-57280228">{{sortText}}</text><text class="iconfont icon-arrow-down data-v-57280228"></text></view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="filter-item data-v-57280228" bindtap="__e"><text class="data-v-57280228">{{salaryFilterText}}</text><text class="iconfont icon-arrow-down data-v-57280228"></text></view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="filter-item data-v-57280228" bindtap="__e"><text class="data-v-57280228">{{industryFilterText}}</text><text class="iconfont icon-arrow-down data-v-57280228"></text></view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="filter-item data-v-57280228" bindtap="__e"><text class="data-v-57280228">{{jobTypeFilterText}}</text><text class="iconfont icon-arrow-down data-v-57280228"></text></view></view><scroll-view class="job-list data-v-57280228" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><block wx:for="{{$root.l0}}" wx:for-item="job" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['goToJobDetail',['$0'],[[['jobList','id',job.$orig.id,'id']]]]]]]}}" class="job-item data-v-57280228" bindtap="__e"><view class="job-header data-v-57280228"><view class="job-info data-v-57280228"><text class="job-title data-v-57280228">{{job.$orig.title}}</text><text class="salary data-v-57280228">{{job.m0}}</text></view><view class="company-logo data-v-57280228"><image src="{{job.$orig.companyLogo||'/static/default-company.png'}}" mode="aspectFill" class="data-v-57280228"></image></view></view><view class="job-tags data-v-57280228"><block wx:for="{{job.$orig.tags}}" wx:for-item="tag" wx:for-index="__i1__" wx:key="*this"><text class="tag data-v-57280228">{{''+tag+''}}</text></block></view><view class="job-meta data-v-57280228"><view class="meta-item data-v-57280228"><text class="iconfont icon-location data-v-57280228"></text><text class="data-v-57280228">{{job.$orig.location}}</text></view><view class="meta-item data-v-57280228"><text class="iconfont icon-experience data-v-57280228"></text><text class="data-v-57280228">{{job.$orig.experience}}</text></view><view class="meta-item data-v-57280228"><text class="iconfont icon-education data-v-57280228"></text><text class="data-v-57280228">{{job.$orig.education}}</text></view></view><view class="job-footer data-v-57280228"><view class="company-info data-v-57280228"><text class="company-name data-v-57280228">{{job.$orig.companyName}}</text><text class="company-scale data-v-57280228">{{job.$orig.companyScale}}</text></view><view class="publish-time data-v-57280228"><text class="data-v-57280228">{{job.m1}}</text></view></view></view></block><block wx:if="{{hasMore}}"><view class="load-more data-v-57280228"><text class="data-v-57280228">{{loading?'加载中...':'上拉加载更多'}}</text></view></block><block wx:else><block wx:if="{{$root.g0>0}}"><view class="no-more data-v-57280228"><text class="data-v-57280228">没有更多数据了</text></view></block></block><block wx:if="{{$root.g1}}"><view class="empty-state data-v-57280228"><image class="empty-img data-v-57280228" src="/static/empty-job.png"></image><text class="empty-text data-v-57280228">暂无招聘信息</text><button data-event-opts="{{[['tap',[['onRefresh',['$event']]]]]}}" class="refresh-btn data-v-57280228" bindtap="__e">刷新试试</button></view></block></scroll-view><u-popup bind:input="__e" vue-id="8dd740cc-1" mode="bottom" height="60%" border-radius="20" value="{{showLocationPicker}}" data-event-opts="{{[['^input',[['__set_model',['','showLocationPicker','$event',[]]]]]]}}" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><view class="location-picker data-v-57280228"><view class="picker-header data-v-57280228"><text data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" class="cancel-btn data-v-57280228" bindtap="__e">取消</text><text class="title data-v-57280228">选择地区</text><text data-event-opts="{{[['tap',[['confirmLocation',['$event']]]]]}}" class="confirm-btn data-v-57280228" bindtap="__e">确定</text></view><region-picker bind:input="__e" vue-id="{{('8dd740cc-2')+','+('8dd740cc-1')}}" value="{{selectedLocation}}" data-event-opts="{{[['^input',[['__set_model',['','selectedLocation','$event',[]]]]]]}}" class="data-v-57280228" bind:__l="__l"></region-picker></view></u-popup><u-popup bind:input="__e" vue-id="8dd740cc-3" mode="bottom" height="40%" border-radius="20" value="{{showSortPicker}}" data-event-opts="{{[['^input',[['__set_model',['','showSortPicker','$event',[]]]]]]}}" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><view class="sort-picker data-v-57280228"><view class="picker-header data-v-57280228"><text class="title data-v-57280228">排序方式</text></view><view class="sort-options data-v-57280228"><block wx:for="{{sortOptions}}" wx:for-item="option" wx:for-index="__i2__" wx:key="value"><view data-event-opts="{{[['tap',[['selectSort',['$0'],[[['sortOptions','value',option.value,'value']]]]]]]}}" class="{{['sort-option','data-v-57280228',(sortBy===option.value)?'active':'']}}" bindtap="__e"><text class="data-v-57280228">{{option.label}}</text><block wx:if="{{sortBy===option.value}}"><text class="iconfont icon-check data-v-57280228"></text></block></view></block></view></view></u-popup><u-popup bind:input="__e" vue-id="8dd740cc-4" mode="bottom" height="50%" border-radius="20" value="{{showSalaryFilter}}" data-event-opts="{{[['^input',[['__set_model',['','showSalaryFilter','$event',[]]]]]]}}" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><view class="salary-filter data-v-57280228"><view class="picker-header data-v-57280228"><text data-event-opts="{{[['tap',[['e6',['$event']]]]]}}" class="cancel-btn data-v-57280228" bindtap="__e">取消</text><text class="title data-v-57280228">薪资范围</text><text data-event-opts="{{[['tap',[['confirmSalaryFilter',['$event']]]]]}}" class="confirm-btn data-v-57280228" bindtap="__e">确定</text></view><view class="salary-options data-v-57280228"><block wx:for="{{salaryOptions}}" wx:for-item="option" wx:for-index="__i3__" wx:key="value"><view data-event-opts="{{[['tap',[['selectSalaryRange',['$0'],[[['salaryOptions','value',option.value,'value']]]]]]]}}" class="{{['salary-option','data-v-57280228',(salaryRange===option.value)?'active':'']}}" bindtap="__e"><text class="data-v-57280228">{{option.label}}</text><block wx:if="{{salaryRange===option.value}}"><text class="iconfont icon-check data-v-57280228"></text></block></view></block></view></view></u-popup></view>