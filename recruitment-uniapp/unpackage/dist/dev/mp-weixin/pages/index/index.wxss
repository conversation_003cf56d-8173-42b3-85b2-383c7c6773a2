@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.job-list-container.data-v-57280228 {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}
.job-list-container .search-bar.data-v-57280228 {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;
}
.job-list-container .search-bar .search-input.data-v-57280228 {
  flex: 1;
  display: flex;
  align-items: center;
  height: 72rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 36rpx;
  margin-right: 20rpx;
}
.job-list-container .search-bar .search-input .iconfont.data-v-57280228 {
  font-size: 32rpx;
  color: #999;
  margin-right: 16rpx;
}
.job-list-container .search-bar .search-input .placeholder.data-v-57280228 {
  font-size: 28rpx;
  color: #999;
}
.job-list-container .search-bar .location-btn.data-v-57280228 {
  display: flex;
  align-items: center;
  padding: 0 16rpx;
}
.job-list-container .search-bar .location-btn .location-text.data-v-57280228 {
  font-size: 28rpx;
  color: #333;
  margin-right: 8rpx;
}
.job-list-container .search-bar .location-btn .iconfont.data-v-57280228 {
  font-size: 24rpx;
  color: #999;
}
.job-list-container .filter-bar.data-v-57280228 {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;
}
.job-list-container .filter-bar .filter-item.data-v-57280228 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
}
.job-list-container .filter-bar .filter-item .iconfont.data-v-57280228 {
  font-size: 20rpx;
  color: #999;
  margin-left: 8rpx;
}
.job-list-container .job-list.data-v-57280228 {
  flex: 1;
  padding: 0 32rpx;
}
.job-list-container .job-list .job-item.data-v-57280228 {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 24rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.job-list-container .job-list .job-item .job-header.data-v-57280228 {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.job-list-container .job-list .job-item .job-header .job-info.data-v-57280228 {
  flex: 1;
}
.job-list-container .job-list .job-item .job-header .job-info .job-title.data-v-57280228 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}
.job-list-container .job-list .job-item .job-header .job-info .salary.data-v-57280228 {
  display: block;
  font-size: 28rpx;
  color: #ff4757;
  font-weight: 600;
}
.job-list-container .job-list .job-item .job-header .company-logo.data-v-57280228 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.job-list-container .job-list .job-item .job-header .company-logo image.data-v-57280228 {
  width: 100%;
  height: 100%;
}
.job-list-container .job-list .job-item .job-tags.data-v-57280228 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.job-list-container .job-list .job-item .job-tags .tag.data-v-57280228 {
  padding: 8rpx 16rpx;
  background: #f0f8ff;
  color: #007aff;
  font-size: 24rpx;
  border-radius: 8rpx;
}
.job-list-container .job-list .job-item .job-meta.data-v-57280228 {
  display: flex;
  align-items: center;
  gap: 32rpx;
  margin-bottom: 24rpx;
}
.job-list-container .job-list .job-item .job-meta .meta-item.data-v-57280228 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}
.job-list-container .job-list .job-item .job-meta .meta-item .iconfont.data-v-57280228 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.job-list-container .job-list .job-item .job-footer.data-v-57280228 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.job-list-container .job-list .job-item .job-footer .company-info .company-name.data-v-57280228 {
  font-size: 28rpx;
  color: #333;
  margin-right: 16rpx;
}
.job-list-container .job-list .job-item .job-footer .company-info .company-scale.data-v-57280228 {
  font-size: 24rpx;
  color: #666;
}
.job-list-container .job-list .job-item .job-footer .publish-time.data-v-57280228 {
  font-size: 24rpx;
  color: #999;
}
.job-list-container .job-list .load-more.data-v-57280228,
.job-list-container .job-list .no-more.data-v-57280228 {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}
.job-list-container .job-list .empty-state.data-v-57280228 {
  text-align: center;
  padding: 120rpx 0;
}
.job-list-container .job-list .empty-state .empty-img.data-v-57280228 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}
.job-list-container .job-list .empty-state .empty-text.data-v-57280228 {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.job-list-container .job-list .empty-state .refresh-btn.data-v-57280228 {
  width: 200rpx;
  height: 64rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 32rpx;
  font-size: 28rpx;
}
.location-picker .picker-header.data-v-57280228,
.sort-picker .picker-header.data-v-57280228,
.salary-filter .picker-header.data-v-57280228 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.location-picker .picker-header .cancel-btn.data-v-57280228,
.location-picker .picker-header .confirm-btn.data-v-57280228,
.sort-picker .picker-header .cancel-btn.data-v-57280228,
.sort-picker .picker-header .confirm-btn.data-v-57280228,
.salary-filter .picker-header .cancel-btn.data-v-57280228,
.salary-filter .picker-header .confirm-btn.data-v-57280228 {
  font-size: 28rpx;
  color: #007aff;
}
.location-picker .picker-header .title.data-v-57280228,
.sort-picker .picker-header .title.data-v-57280228,
.salary-filter .picker-header .title.data-v-57280228 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.sort-options .sort-option.data-v-57280228,
.sort-options .salary-option.data-v-57280228,
.salary-options .sort-option.data-v-57280228,
.salary-options .salary-option.data-v-57280228 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f8f9fa;
  font-size: 28rpx;
  color: #333;
}
.sort-options .sort-option.active.data-v-57280228,
.sort-options .salary-option.active.data-v-57280228,
.salary-options .sort-option.active.data-v-57280228,
.salary-options .salary-option.active.data-v-57280228 {
  color: #007aff;
  background: #f0f8ff;
}
.sort-options .sort-option .iconfont.data-v-57280228,
.sort-options .salary-option .iconfont.data-v-57280228,
.salary-options .sort-option .iconfont.data-v-57280228,
.salary-options .salary-option .iconfont.data-v-57280228 {
  font-size: 32rpx;
  color: #007aff;
}
