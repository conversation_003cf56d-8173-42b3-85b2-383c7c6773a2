<view class="profile-container data-v-60cb334c"><view class="user-card data-v-60cb334c"><block wx:if="{{!isLoggedIn}}"><view data-event-opts="{{[['tap',[['goToLogin',['$event']]]]]}}" class="user-info data-v-60cb334c" bindtap="__e"><view class="avatar data-v-60cb334c"><image src="/static/default-avatar.png" mode="aspectFill" class="data-v-60cb334c"></image></view><view class="user-details data-v-60cb334c"><text class="username data-v-60cb334c">点击登录</text><text class="user-desc data-v-60cb334c">登录后享受更多服务</text></view><text class="iconfont icon-arrow-right data-v-60cb334c"></text></view></block><block wx:else><view class="user-info data-v-60cb334c"><view class="avatar data-v-60cb334c"><image src="{{userInfo.avatar||'/static/default-avatar.png'}}" mode="aspectFill" class="data-v-60cb334c"></image></view><view class="user-details data-v-60cb334c"><text class="username data-v-60cb334c">{{userInfo.name||'用户'}}</text><text class="user-desc data-v-60cb334c">{{userInfo.phone||'未绑定手机号'}}</text></view><block wx:if="{{membershipInfo.isMember}}"><view class="member-badge data-v-60cb334c"><text class="data-v-60cb334c">会员</text></view></block></view></block><block wx:if="{{isLoggedIn}}"><view class="stats-info data-v-60cb334c"><view data-event-opts="{{[['tap',[['goToConsumptionRecords',['$event']]]]]}}" class="stats-item data-v-60cb334c" bindtap="__e"><text class="stats-value data-v-60cb334c">{{membershipInfo.remainingViews||0}}</text><text class="stats-label data-v-60cb334c">剩余次数</text></view><view data-event-opts="{{[['tap',[['goToCollections',['$event']]]]]}}" class="stats-item data-v-60cb334c" bindtap="__e"><text class="stats-value data-v-60cb334c">{{collectCount}}</text><text class="stats-label data-v-60cb334c">收藏职位</text></view><view data-event-opts="{{[['tap',[['goToApplications',['$event']]]]]}}" class="stats-item data-v-60cb334c" bindtap="__e"><text class="stats-value data-v-60cb334c">{{applicationCount}}</text><text class="stats-label data-v-60cb334c">投递记录</text></view></view></block></view><view class="menu-section data-v-60cb334c"><view class="menu-title data-v-60cb334c">我的服务</view><view class="menu-list data-v-60cb334c"><view data-event-opts="{{[['tap',[['goToMemberCenter',['$event']]]]]}}" class="menu-item data-v-60cb334c" bindtap="__e"><view class="menu-icon data-v-60cb334c"><text class="iconfont icon-vip data-v-60cb334c"></text></view><text class="menu-text data-v-60cb334c">会员中心</text><block wx:if="{{!membershipInfo.isMember}}"><view class="menu-badge data-v-60cb334c"><text class="data-v-60cb334c">开通</text></view></block><text class="iconfont icon-arrow-right data-v-60cb334c"></text></view><view data-event-opts="{{[['tap',[['goToConsumptionRecords',['$event']]]]]}}" class="menu-item data-v-60cb334c" bindtap="__e"><view class="menu-icon data-v-60cb334c"><text class="iconfont icon-bill data-v-60cb334c"></text></view><text class="menu-text data-v-60cb334c">消费记录</text><text class="iconfont icon-arrow-right data-v-60cb334c"></text></view><view data-event-opts="{{[['tap',[['goToCollections',['$event']]]]]}}" class="menu-item data-v-60cb334c" bindtap="__e"><view class="menu-icon data-v-60cb334c"><text class="iconfont icon-heart data-v-60cb334c"></text></view><text class="menu-text data-v-60cb334c">收藏职位</text><text class="iconfont icon-arrow-right data-v-60cb334c"></text></view><view data-event-opts="{{[['tap',[['goToApplications',['$event']]]]]}}" class="menu-item data-v-60cb334c" bindtap="__e"><view class="menu-icon data-v-60cb334c"><text class="iconfont icon-send data-v-60cb334c"></text></view><text class="menu-text data-v-60cb334c">投递记录</text><text class="iconfont icon-arrow-right data-v-60cb334c"></text></view></view></view><view class="menu-section data-v-60cb334c"><view class="menu-title data-v-60cb334c">工具</view><view class="menu-list data-v-60cb334c"><view data-event-opts="{{[['tap',[['goToSettings',['$event']]]]]}}" class="menu-item data-v-60cb334c" bindtap="__e"><view class="menu-icon data-v-60cb334c"><text class="iconfont icon-settings data-v-60cb334c"></text></view><text class="menu-text data-v-60cb334c">设置</text><text class="iconfont icon-arrow-right data-v-60cb334c"></text></view><view data-event-opts="{{[['tap',[['contactService',['$event']]]]]}}" class="menu-item data-v-60cb334c" bindtap="__e"><view class="menu-icon data-v-60cb334c"><text class="iconfont icon-service data-v-60cb334c"></text></view><text class="menu-text data-v-60cb334c">客服咨询</text><text class="iconfont icon-arrow-right data-v-60cb334c"></text></view><view data-event-opts="{{[['tap',[['showAbout',['$event']]]]]}}" class="menu-item data-v-60cb334c" bindtap="__e"><view class="menu-icon data-v-60cb334c"><text class="iconfont icon-info data-v-60cb334c"></text></view><text class="menu-text data-v-60cb334c">关于我们</text><text class="iconfont icon-arrow-right data-v-60cb334c"></text></view></view></view><block wx:if="{{isLoggedIn}}"><view class="logout-section data-v-60cb334c"><button data-event-opts="{{[['tap',[['handleLogout',['$event']]]]]}}" class="logout-btn data-v-60cb334c" bindtap="__e">退出登录</button></view></block><u-popup bind:input="__e" vue-id="23b2e5ec-1" mode="center" width="80%" border-radius="20" value="{{showAboutModal}}" data-event-opts="{{[['^input',[['__set_model',['','showAboutModal','$event',[]]]]]]}}" class="data-v-60cb334c" bind:__l="__l" vue-slots="{{['default']}}"><view class="about-modal data-v-60cb334c"><view class="modal-header data-v-60cb334c"><text class="modal-title data-v-60cb334c">关于我们</text><text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="close-btn data-v-60cb334c" bindtap="__e">×</text></view><view class="about-content data-v-60cb334c"><view class="app-info data-v-60cb334c"><image class="app-logo data-v-60cb334c" src="/static/logo.png"></image><text class="app-name data-v-60cb334c">招聘平台</text><text class="app-version data-v-60cb334c">版本 1.0.0</text></view><view class="app-desc data-v-60cb334c"><text class="data-v-60cb334c">专业的招聘信息平台，为求职者提供优质的工作机会，为企业提供精准的人才匹配服务。</text></view><view class="contact-info data-v-60cb334c"><view class="contact-item data-v-60cb334c"><text class="label data-v-60cb334c">客服电话：</text><text class="value data-v-60cb334c">400-888-8888</text></view><view class="contact-item data-v-60cb334c"><text class="label data-v-60cb334c">客服微信：</text><text class="value data-v-60cb334c">kf888888</text></view><view class="contact-item data-v-60cb334c"><text class="label data-v-60cb334c">官方网站：</text><text class="value data-v-60cb334c">www.recruitment.com</text></view></view></view></view></u-popup></view>