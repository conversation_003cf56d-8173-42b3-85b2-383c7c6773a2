@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.profile-container.data-v-60cb334c {
  min-height: 100vh;
  background: #f8f9fa;
}
.profile-container .user-card.data-v-60cb334c {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
}
.profile-container .user-card .user-info.data-v-60cb334c {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.profile-container .user-card .user-info .avatar.data-v-60cb334c {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  overflow: hidden;
  margin-right: 24rpx;
}
.profile-container .user-card .user-info .avatar image.data-v-60cb334c {
  width: 100%;
  height: 100%;
}
.profile-container .user-card .user-info .user-details.data-v-60cb334c {
  flex: 1;
}
.profile-container .user-card .user-info .user-details .username.data-v-60cb334c {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8rpx;
}
.profile-container .user-card .user-info .user-details .user-desc.data-v-60cb334c {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.profile-container .user-card .user-info .member-badge.data-v-60cb334c {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  margin-right: 16rpx;
}
.profile-container .user-card .user-info .icon-arrow-right.data-v-60cb334c {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.profile-container .user-card .stats-info.data-v-60cb334c {
  display: flex;
  justify-content: space-around;
}
.profile-container .user-card .stats-info .stats-item.data-v-60cb334c {
  text-align: center;
}
.profile-container .user-card .stats-info .stats-item .stats-value.data-v-60cb334c {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8rpx;
}
.profile-container .user-card .stats-info .stats-item .stats-label.data-v-60cb334c {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.profile-container .menu-section.data-v-60cb334c {
  margin: 40rpx;
}
.profile-container .menu-section .menu-title.data-v-60cb334c {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}
.profile-container .menu-section .menu-list.data-v-60cb334c {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}
.profile-container .menu-section .menu-list .menu-item.data-v-60cb334c {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f8f9fa;
}
.profile-container .menu-section .menu-list .menu-item.data-v-60cb334c:last-child {
  border-bottom: none;
}
.profile-container .menu-section .menu-list .menu-item .menu-icon.data-v-60cb334c {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f8ff;
  border-radius: 36rpx;
  margin-right: 24rpx;
}
.profile-container .menu-section .menu-list .menu-item .menu-icon .iconfont.data-v-60cb334c {
  font-size: 32rpx;
  color: #007aff;
}
.profile-container .menu-section .menu-list .menu-item .menu-text.data-v-60cb334c {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.profile-container .menu-section .menu-list .menu-item .menu-badge.data-v-60cb334c {
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
}
.profile-container .menu-section .menu-list .menu-item .icon-arrow-right.data-v-60cb334c {
  font-size: 24rpx;
  color: #ccc;
}
.profile-container .logout-section.data-v-60cb334c {
  margin: 40rpx;
}
.profile-container .logout-section .logout-btn.data-v-60cb334c {
  width: 100%;
  height: 88rpx;
  background: #fff;
  color: #ff4757;
  border: 2rpx solid #ff4757;
  border-radius: 44rpx;
  font-size: 28rpx;
}
.about-modal.data-v-60cb334c {
  padding: 40rpx;
}
.about-modal .modal-header.data-v-60cb334c {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.about-modal .modal-header .modal-title.data-v-60cb334c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.about-modal .modal-header .close-btn.data-v-60cb334c {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}
.about-modal .about-content .app-info.data-v-60cb334c {
  text-align: center;
  margin-bottom: 40rpx;
}
.about-modal .about-content .app-info .app-logo.data-v-60cb334c {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
}
.about-modal .about-content .app-info .app-name.data-v-60cb334c {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.about-modal .about-content .app-info .app-version.data-v-60cb334c {
  display: block;
  font-size: 24rpx;
  color: #999;
}
.about-modal .about-content .app-desc.data-v-60cb334c {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40rpx;
}
.about-modal .about-content .contact-info .contact-item.data-v-60cb334c {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.about-modal .about-content .contact-info .contact-item .label.data-v-60cb334c {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}
.about-modal .about-content .contact-info .contact-item .value.data-v-60cb334c {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
