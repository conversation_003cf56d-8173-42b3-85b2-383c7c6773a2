@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.settings-container.data-v-058d5c7c {
  min-height: 100vh;
  background: #f8f9fa;
}
.settings-container .settings-section.data-v-058d5c7c {
  margin: 40rpx;
}
.settings-container .settings-section .section-title.data-v-058d5c7c {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}
.settings-container .settings-section .settings-list.data-v-058d5c7c {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}
.settings-container .settings-section .settings-list .setting-item.data-v-058d5c7c {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f8f9fa;
}
.settings-container .settings-section .settings-list .setting-item.data-v-058d5c7c:last-child {
  border-bottom: none;
}
.settings-container .settings-section .settings-list .setting-item .setting-info.data-v-058d5c7c {
  flex: 1;
}
.settings-container .settings-section .settings-list .setting-item .setting-info .setting-title.data-v-058d5c7c {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}
.settings-container .settings-section .settings-list .setting-item .setting-info .setting-desc.data-v-058d5c7c {
  display: block;
  font-size: 24rpx;
  color: #999;
}
.settings-container .settings-section .settings-list .setting-item .iconfont.data-v-058d5c7c {
  font-size: 24rpx;
  color: #ccc;
}
