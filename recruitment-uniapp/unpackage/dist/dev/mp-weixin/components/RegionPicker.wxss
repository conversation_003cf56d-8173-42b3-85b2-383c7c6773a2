@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.region-picker .region-btn.data-v-36aca27d {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}
.region-picker .region-btn .region-text.data-v-36aca27d {
  font-size: 28rpx;
  color: #333;
}
.region-picker .region-btn .iconfont.data-v-36aca27d {
  font-size: 24rpx;
  color: #999;
}
.picker-container.data-v-36aca27d {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.picker-container .picker-header.data-v-36aca27d {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.picker-container .picker-header .cancel-btn.data-v-36aca27d,
.picker-container .picker-header .confirm-btn.data-v-36aca27d {
  font-size: 28rpx;
  color: #007aff;
}
.picker-container .picker-header .title.data-v-36aca27d {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.picker-container .picker-content.data-v-36aca27d {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.picker-container .picker-content .tabs.data-v-36aca27d {
  display: flex;
  border-bottom: 2rpx solid #f0f0f0;
}
.picker-container .picker-content .tabs .tab-item.data-v-36aca27d {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.picker-container .picker-content .tabs .tab-item.active.data-v-36aca27d {
  color: #007aff;
}
.picker-container .picker-content .tabs .tab-item.active.data-v-36aca27d::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #007aff;
  border-radius: 2rpx;
}
.picker-container .picker-content .list-container.data-v-36aca27d {
  flex: 1;
}
.picker-container .picker-content .list-container .list .list-item.data-v-36aca27d {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f8f9fa;
  font-size: 28rpx;
  color: #333;
}
.picker-container .picker-content .list-container .list .list-item.selected.data-v-36aca27d {
  color: #007aff;
  background: #f0f8ff;
}
.picker-container .picker-content .list-container .list .list-item .iconfont.data-v-36aca27d {
  font-size: 32rpx;
  color: #007aff;
}
