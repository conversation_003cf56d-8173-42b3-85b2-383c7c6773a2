<view class="region-picker data-v-36aca27d"><view data-event-opts="{{[['tap',[['showPicker',['$event']]]]]}}" class="region-btn data-v-36aca27d" bindtap="__e"><text class="region-text data-v-36aca27d">{{selectedRegionText||'选择地区'}}</text><text class="iconfont icon-arrow-down data-v-36aca27d"></text></view><u-popup bind:input="__e" vue-id="c22fa3ae-1" mode="bottom" height="60%" border-radius="20" value="{{visible}}" data-event-opts="{{[['^input',[['__set_model',['','visible','$event',[]]]]]]}}" class="data-v-36aca27d" bind:__l="__l" vue-slots="{{['default']}}"><view class="picker-container data-v-36aca27d"><view class="picker-header data-v-36aca27d"><text data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="cancel-btn data-v-36aca27d" bindtap="__e">取消</text><text class="title data-v-36aca27d">选择地区</text><text data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="confirm-btn data-v-36aca27d" bindtap="__e">确定</text></view><view class="picker-content data-v-36aca27d"><view class="tabs data-v-36aca27d"><view data-event-opts="{{[['tap',[['switchTab',[0]]]]]}}" class="{{['tab-item','data-v-36aca27d',(activeTab===0)?'active':'']}}" bindtap="__e">{{''+(provinces[selectedProvinceIndex]&&provinces[selectedProvinceIndex].name||'省份')+''}}</view><block wx:if="{{$root.g0>0}}"><view data-event-opts="{{[['tap',[['switchTab',[1]]]]]}}" class="{{['tab-item','data-v-36aca27d',(activeTab===1)?'active':'']}}" bindtap="__e">{{''+(cities[selectedCityIndex]&&cities[selectedCityIndex].name||'城市')+''}}</view></block><block wx:if="{{$root.g1>0}}"><view data-event-opts="{{[['tap',[['switchTab',[2]]]]]}}" class="{{['tab-item','data-v-36aca27d',(activeTab===2)?'active':'']}}" bindtap="__e">{{''+(districts[selectedDistrictIndex]&&districts[selectedDistrictIndex].name||'区县')+''}}</view></block></view><scroll-view class="list-container data-v-36aca27d" scroll-y="{{true}}"><block wx:if="{{activeTab===0}}"><view class="list data-v-36aca27d"><block wx:for="{{provinces}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['selectProvince',[index]]]]]}}" class="{{['list-item','data-v-36aca27d',(selectedProvinceIndex===index)?'selected':'']}}" bindtap="__e"><text class="data-v-36aca27d">{{item.name}}</text><block wx:if="{{selectedProvinceIndex===index}}"><text class="iconfont icon-check data-v-36aca27d"></text></block></view></block></view></block><block wx:if="{{activeTab===1}}"><view class="list data-v-36aca27d"><block wx:for="{{cities}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['selectCity',[index]]]]]}}" class="{{['list-item','data-v-36aca27d',(selectedCityIndex===index)?'selected':'']}}" bindtap="__e"><text class="data-v-36aca27d">{{item.name}}</text><block wx:if="{{selectedCityIndex===index}}"><text class="iconfont icon-check data-v-36aca27d"></text></block></view></block></view></block><block wx:if="{{activeTab===2}}"><view class="list data-v-36aca27d"><block wx:for="{{districts}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['selectDistrict',[index]]]]]}}" class="{{['list-item','data-v-36aca27d',(selectedDistrictIndex===index)?'selected':'']}}" bindtap="__e"><text class="data-v-36aca27d">{{item.name}}</text><block wx:if="{{selectedDistrictIndex===index}}"><text class="iconfont icon-check data-v-36aca27d"></text></block></view></block></view></block></scroll-view></view></view></u-popup></view>