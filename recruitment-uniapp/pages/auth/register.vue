<template>
  <view class="register-container">
    <!-- 头部 -->
    <view class="header">
      <text class="title">用户注册</text>
      <text class="subtitle">完善个人信息，开启求职之旅</text>
    </view>

    <!-- 注册表单 -->
    <view class="form">
      <!-- 头像上传 -->
      <view class="form-item avatar-item">
        <text class="label">头像</text>
        <view class="avatar-upload" @click="chooseAvatar">
          <image v-if="form.avatar" :src="form.avatar" class="avatar-img" mode="aspectFill"></image>
          <view v-else class="avatar-placeholder">
            <text class="iconfont icon-camera"></text>
            <text class="placeholder-text">上传头像</text>
          </view>
        </view>
      </view>

      <!-- 姓名 -->
      <view class="form-item">
        <text class="label">姓名 <text class="required">*</text></text>
        <input class="input" type="text" placeholder="请输入真实姓名" v-model="form.name" />
      </view>

      <!-- 手机号 -->
      <view class="form-item">
        <text class="label">手机号 <text class="required">*</text></text>
        <input class="input" type="number" placeholder="请输入手机号" v-model="form.phone" maxlength="11" />
      </view>

      <!-- 验证码 -->
      <view class="form-item">
        <text class="label">验证码 <text class="required">*</text></text>
        <view class="code-input">
          <input class="input" type="number" placeholder="请输入验证码" v-model="form.code" maxlength="6" />
          <button class="code-btn" :disabled="codeDisabled" @click="sendCode">
            {{ codeText }}
          </button>
        </view>
      </view>

      <!-- 性别 -->
      <view class="form-item">
        <text class="label">性别</text>
        <radio-group class="radio-group" @change="onGenderChange">
          <label class="radio-item">
            <radio value="1" :checked="form.gender === '1'" color="#007aff" />
            <text>男</text>
          </label>
          <label class="radio-item">
            <radio value="2" :checked="form.gender === '2'" color="#007aff" />
            <text>女</text>
          </label>
        </radio-group>
      </view>

      <!-- 年龄 -->
      <view class="form-item">
        <text class="label">年龄</text>
        <input class="input" type="number" placeholder="请输入年龄" v-model="form.age" maxlength="2" />
      </view>

      <!-- 地区选择 -->
      <view class="form-item">
        <text class="label">所在地区</text>
        <RegionPicker v-model="form.region" />
      </view>

      <!-- 期望职位 -->
      <view class="form-item">
        <text class="label">期望职位</text>
        <input class="input" type="text" placeholder="请输入期望职位" v-model="form.expectedJob" />
      </view>

      <!-- 期望薪资 -->
      <view class="form-item">
        <text class="label">期望薪资</text>
        <view class="salary-input">
          <input class="input salary-min" type="number" placeholder="最低" v-model="form.minSalary" />
          <text class="separator">-</text>
          <input class="input salary-max" type="number" placeholder="最高" v-model="form.maxSalary" />
          <text class="unit">元/月</text>
        </view>
      </view>

      <!-- 工作经验 -->
      <view class="form-item">
        <text class="label">工作经验</text>
        <picker :range="experienceOptions" :value="experienceIndex" @change="onExperienceChange">
          <view class="picker-input">
            <text class="picker-text">{{ experienceOptions[experienceIndex] || '请选择工作经验' }}</text>
            <text class="iconfont icon-arrow-down"></text>
          </view>
        </picker>
      </view>

      <!-- 学历 -->
      <view class="form-item">
        <text class="label">学历</text>
        <picker :range="educationOptions" :value="educationIndex" @change="onEducationChange">
          <view class="picker-input">
            <text class="picker-text">{{ educationOptions[educationIndex] || '请选择学历' }}</text>
            <text class="iconfont icon-arrow-down"></text>
          </view>
        </picker>
      </view>

      <!-- 个人简介 -->
      <view class="form-item">
        <text class="label">个人简介</text>
        <textarea class="textarea" placeholder="请简单介绍一下自己的工作经历和技能特长" v-model="form.introduction"
          maxlength="500"></textarea>
        <text class="char-count">{{ form.introduction.length }}/500</text>
      </view>
    </view>

    <!-- 用户协议 -->
    <view class="agreement">
      <checkbox-group @change="onAgreementChange">
        <checkbox :checked="agreed" color="#007aff" />
      </checkbox-group>
      <text class="agreement-text">
        我已阅读并同意
        <text class="link" @click="showAgreement('user')">《用户协议》</text>
        和
        <text class="link" @click="showAgreement('privacy')">《隐私政策》</text>
      </text>
    </view>

    <!-- 注册按钮 -->
    <button class="register-btn" :disabled="!canSubmit" :loading="loading" @click="handleRegister">
      立即注册
    </button>
  </view>
</template>

<script>
import RegionPicker from '@/components/RegionPicker.vue'
import { authApi } from '@/utils/api.js'
import { auth, validatePhone, showToast, showLoading, hideLoading } from '@/utils/utils.js'

export default {
  name: 'Register',
  components: {
    RegionPicker
  },
  data() {
    return {
      loading: false,
      agreed: false,
      codeDisabled: false,
      codeCountdown: 0,
      codeTimer: null,
      experienceIndex: -1,
      educationIndex: -1,
      form: {
        avatar: '',
        name: '',
        phone: '',
        code: '',
        gender: '',
        age: '',
        region: {},
        expectedJob: '',
        minSalary: '',
        maxSalary: '',
        experience: '',
        education: '',
        introduction: ''
      },
      experienceOptions: [
        '应届毕业生',
        '1年以下',
        '1-3年',
        '3-5年',
        '5-10年',
        '10年以上'
      ],
      educationOptions: [
        '初中及以下',
        '高中/中专',
        '大专',
        '本科',
        '硕士',
        '博士'
      ]
    }
  },
  computed: {
    canSubmit() {
      return this.form.name.trim() &&
        validatePhone(this.form.phone) &&
        this.form.code.length === 6 &&
        this.agreed
    },
    codeText() {
      return this.codeCountdown > 0 ? `${this.codeCountdown}s` : '获取验证码'
    }
  },
  onUnload() {
    if (this.codeTimer) {
      clearInterval(this.codeTimer)
    }
  },
  methods: {
    // 选择头像
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.form.avatar = res.tempFilePaths[0]
          // 这里可以上传到服务器
          this.uploadAvatar(res.tempFilePaths[0])
        }
      })
    },

    // 上传头像
    async uploadAvatar(filePath) {
      try {
        showLoading('上传中...')
        // 这里调用上传接口
        // const res = await uploadApi.uploadImage(filePath)
        // this.form.avatar = res.data.url
      } catch (error) {
        showToast('头像上传失败')
      } finally {
        hideLoading()
      }
    },

    // 发送验证码
    async sendCode() {
      if (!validatePhone(this.form.phone)) {
        showToast('请输入正确的手机号')
        return
      }

      try {
        // 这里应该调用发送验证码的接口
        // await authApi.sendCode(this.form.phone)

        showToast('验证码已发送')
        this.startCountdown()
      } catch (error) {
        showToast('发送验证码失败')
      }
    },

    // 开始倒计时
    startCountdown() {
      this.codeDisabled = true
      this.codeCountdown = 60

      this.codeTimer = setInterval(() => {
        this.codeCountdown--
        if (this.codeCountdown <= 0) {
          clearInterval(this.codeTimer)
          this.codeDisabled = false
        }
      }, 1000)
    },

    // 性别变化
    onGenderChange(e) {
      this.form.gender = e.detail.value
    },

    // 工作经验变化
    onExperienceChange(e) {
      this.experienceIndex = e.detail.value
      this.form.experience = this.experienceOptions[e.detail.value]
    },

    // 学历变化
    onEducationChange(e) {
      this.educationIndex = e.detail.value
      this.form.education = this.educationOptions[e.detail.value]
    },

    // 同意协议变化
    onAgreementChange(e) {
      this.agreed = e.detail.value.length > 0
    },

    // 显示协议
    showAgreement(type) {
      const url = type === 'user' ? '/pages/agreement/user' : '/pages/agreement/privacy'
      uni.navigateTo({ url })
    },

    // 注册
    async handleRegister() {
      if (!this.canSubmit) return

      try {
        this.loading = true

        const registerData = {
          ...this.form,
          regionId: (this.form.region.district && this.form.region.district.id) ||
            (this.form.region.city && this.form.region.city.id) ||
            (this.form.region.province && this.form.region.province.id)
        }

        const res = await authApi.register(registerData)

        if (res.code === 200) {
          showToast('注册成功', 'success')

          // 自动登录
          auth.setToken(res.data.token)
          auth.setUserInfo(res.data.userInfo)

          setTimeout(() => {
            uni.switchTab({
              url: '/pages/index/index'
            })
          }, 1000)
        } else {
          showToast(res.msg || '注册失败')
        }
      } catch (error) {
        console.error('注册失败:', error)
        showToast('注册失败，请重试')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 40rpx;

  .header {
    text-align: center;
    margin-bottom: 60rpx;

    .title {
      display: block;
      font-size: 48rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 16rpx;
    }

    .subtitle {
      display: block;
      font-size: 28rpx;
      color: #666;
    }
  }

  .form {
    background: #fff;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;

    .form-item {
      margin-bottom: 40rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;

        .required {
          color: #ff4757;
        }
      }

      .input {
        width: 100%;
        height: 80rpx;
        padding: 0 24rpx;
        border: 2rpx solid #e9ecef;
        border-radius: 12rpx;
        font-size: 28rpx;
        background: #f8f9fa;
      }

      .textarea {
        width: 100%;
        min-height: 160rpx;
        padding: 24rpx;
        border: 2rpx solid #e9ecef;
        border-radius: 12rpx;
        font-size: 28rpx;
        background: #f8f9fa;
        resize: none;
      }

      .char-count {
        display: block;
        text-align: right;
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
      }

      &.avatar-item {
        .avatar-upload {
          width: 120rpx;
          height: 120rpx;
          border-radius: 60rpx;
          border: 2rpx solid #e9ecef;
          overflow: hidden;

          .avatar-img {
            width: 100%;
            height: 100%;
          }

          .avatar-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;

            .iconfont {
              font-size: 32rpx;
              color: #999;
              margin-bottom: 8rpx;
            }

            .placeholder-text {
              font-size: 20rpx;
              color: #999;
            }
          }
        }
      }

      .code-input {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .input {
          flex: 1;
        }

        .code-btn {
          width: 200rpx;
          height: 80rpx;
          background: #007aff;
          color: #fff;
          border: none;
          border-radius: 12rpx;
          font-size: 24rpx;

          &:disabled {
            background: #ccc;
          }
        }
      }

      .radio-group {
        display: flex;
        gap: 40rpx;

        .radio-item {
          display: flex;
          align-items: center;
          gap: 8rpx;
          font-size: 28rpx;
          color: #333;
        }
      }

      .salary-input {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .salary-min,
        .salary-max {
          flex: 1;
        }

        .separator {
          font-size: 28rpx;
          color: #666;
        }

        .unit {
          font-size: 28rpx;
          color: #666;
        }
      }

      .picker-input {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 80rpx;
        padding: 0 24rpx;
        border: 2rpx solid #e9ecef;
        border-radius: 12rpx;
        background: #f8f9fa;

        .picker-text {
          font-size: 28rpx;
          color: #333;
        }

        .iconfont {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }

  .agreement {
    display: flex;
    align-items: flex-start;
    margin-bottom: 40rpx;
    font-size: 24rpx;
    color: #666;

    checkbox-group {
      margin-right: 16rpx;
      margin-top: 4rpx;
    }

    .agreement-text {
      flex: 1;
      line-height: 1.5;

      .link {
        color: #007aff;
        text-decoration: underline;
      }
    }
  }

  .register-btn {
    width: 100%;
    height: 88rpx;
    background: #007aff;
    color: #fff;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;

    &:disabled {
      background: #ccc;
    }
  }
}
</style>
