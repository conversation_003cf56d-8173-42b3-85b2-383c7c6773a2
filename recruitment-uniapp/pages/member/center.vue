<template>
  <view class="member-center">
    <!-- 会员状态卡片 -->
    <view class="member-card">
      <view class="card-bg">
        <image src="/static/member-bg.png" mode="aspectFill"></image>
      </view>
      <view class="card-content">
        <view class="member-info">
          <view class="avatar">
            <image :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
          </view>
          <view class="user-info">
            <text class="username">{{ userInfo.name || '用户' }}</text>
            <text class="member-status">{{ membershipInfo.isMember ? '会员用户' : '普通用户' }}</text>
          </view>
        </view>
        
        <view v-if="membershipInfo.isMember" class="member-benefits">
          <view class="benefit-item">
            <text class="benefit-label">剩余次数</text>
            <text class="benefit-value">{{ membershipInfo.remainingViews }}次</text>
          </view>
          <view class="benefit-item">
            <text class="benefit-label">到期时间</text>
            <text class="benefit-value">{{ formatDate(membershipInfo.expireTime) }}</text>
          </view>
        </view>
        
        <view v-else class="upgrade-tip">
          <text class="tip-text">开通会员，享受更多特权</text>
        </view>
      </view>
    </view>
    
    <!-- 会员套餐 -->
    <view class="membership-packages">
      <view class="section-title">会员套餐</view>
      <view class="packages-list">
        <view 
          v-for="pkg in packages" 
          :key="pkg.type"
          class="package-item"
          :class="{ recommended: pkg.recommended }"
          @click="selectPackage(pkg)"
        >
          <view v-if="pkg.recommended" class="recommended-tag">推荐</view>
          <view class="package-header">
            <text class="package-name">{{ pkg.name }}</text>
            <view class="package-price">
              <text class="price">¥{{ pkg.price }}</text>
              <text class="original-price" v-if="pkg.originalPrice">¥{{ pkg.originalPrice }}</text>
            </view>
          </view>
          <view class="package-benefits">
            <view 
              v-for="benefit in pkg.benefits" 
              :key="benefit"
              class="benefit-item"
            >
              <text class="iconfont icon-check"></text>
              <text class="benefit-text">{{ benefit }}</text>
            </view>
          </view>
          <button 
            class="buy-btn" 
            :class="{ primary: pkg.recommended }"
            @click.stop="buyPackage(pkg)"
            :loading="buyLoading === pkg.type"
          >
            {{ membershipInfo.isMember ? '续费' : '立即开通' }}
          </button>
        </view>
      </view>
    </view>
    
    <!-- 功能入口 -->
    <view class="function-entries">
      <view class="entry-item" @click="goToConsumptionRecords">
        <view class="entry-icon">
          <text class="iconfont icon-bill"></text>
        </view>
        <view class="entry-info">
          <text class="entry-title">消费记录</text>
          <text class="entry-desc">查看付费记录和剩余次数</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      
      <view class="entry-item" @click="goToMemberRights">
        <view class="entry-icon">
          <text class="iconfont icon-vip"></text>
        </view>
        <view class="entry-info">
          <text class="entry-title">会员权益</text>
          <text class="entry-desc">了解会员专享特权</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      
      <view class="entry-item" @click="contactService">
        <view class="entry-icon">
          <text class="iconfont icon-service"></text>
        </view>
        <view class="entry-info">
          <text class="entry-title">客服咨询</text>
          <text class="entry-desc">遇到问题？联系客服</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>
    
    <!-- 支付确认弹窗 -->
    <u-popup v-model="showPaymentModal" mode="center" width="80%" border-radius="20">
      <view class="payment-modal">
        <view class="modal-header">
          <text class="modal-title">确认支付</text>
          <text class="close-btn" @click="showPaymentModal = false">×</text>
        </view>
        
        <view class="payment-info">
          <view class="package-info">
            <text class="package-name">{{ selectedPackage.name }}</text>
            <text class="package-desc">{{ selectedPackage.desc }}</text>
          </view>
          <view class="price-info">
            <text class="price">¥{{ selectedPackage.price }}</text>
            <text class="original-price" v-if="selectedPackage.originalPrice">¥{{ selectedPackage.originalPrice }}</text>
          </view>
        </view>
        
        <view class="payment-methods">
          <view class="method-title">支付方式</view>
          <view class="method-item active">
            <text class="iconfont icon-wechat-pay"></text>
            <text class="method-name">微信支付</text>
            <text class="iconfont icon-check"></text>
          </view>
        </view>
        
        <button class="confirm-pay-btn" @click="confirmPayment" :loading="payLoading">
          确认支付 ¥{{ selectedPackage.price }}
        </button>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { paymentApi, authApi } from '@/utils/api.js'
import { auth, formatDate, showToast, showLoading, hideLoading } from '@/utils/utils.js'

export default {
  name: 'MemberCenter',
  data() {
    return {
      userInfo: {},
      membershipInfo: {
        isMember: false,
        remainingViews: 0,
        expireTime: null
      },
      packages: [
        {
          type: 'basic',
          name: '基础会员',
          price: 30,
          originalPrice: null,
          desc: '查看45个联系人',
          recommended: false,
          benefits: [
            '查看45个联系人',
            '30天有效期',
            '专属客服支持'
          ]
        },
        {
          type: 'premium',
          name: '高级会员',
          price: 50,
          originalPrice: 80,
          desc: '查看80个联系人',
          recommended: true,
          benefits: [
            '查看80个联系人',
            '60天有效期',
            '优先客服支持',
            '专属求职指导'
          ]
        }
      ],
      selectedPackage: {},
      showPaymentModal: false,
      buyLoading: '',
      payLoading: false
    }
  },
  onLoad() {
    this.loadUserInfo()
    this.loadMembershipInfo()
  },
  onShow() {
    // 从支付页面返回时刷新数据
    this.loadMembershipInfo()
  },
  methods: {
    formatDate,
    
    // 加载用户信息
    loadUserInfo() {
      this.userInfo = auth.getUserInfo() || {}
    },
    
    // 加载会员信息
    async loadMembershipInfo() {
      try {
        const res = await paymentApi.getMembershipInfo()
        if (res.code === 200) {
          this.membershipInfo = res.data
        }
      } catch (error) {
        console.error('加载会员信息失败:', error)
      }
    },
    
    // 选择套餐
    selectPackage(pkg) {
      this.selectedPackage = pkg
    },
    
    // 购买套餐
    buyPackage(pkg) {
      if (!auth.isLoggedIn()) {
        uni.navigateTo({
          url: '/pages/auth/login'
        })
        return
      }
      
      this.selectedPackage = pkg
      this.showPaymentModal = true
    },
    
    // 确认支付
    async confirmPayment() {
      if (this.payLoading) return
      
      try {
        this.payLoading = true
        
        const res = await paymentApi.buyMembership(this.selectedPackage.type)
        
        if (res.code === 200) {
          // 调用微信支付
          await this.wechatPay(res.data)
          this.showPaymentModal = false
        } else {
          showToast(res.msg || '支付失败')
        }
      } catch (error) {
        console.error('支付失败:', error)
        showToast('支付失败，请重试')
      } finally {
        this.payLoading = false
      }
    },
    
    // 微信支付
    async wechatPay(paymentData) {
      return new Promise((resolve, reject) => {
        uni.requestPayment({
          provider: 'wxpay',
          timeStamp: paymentData.timeStamp,
          nonceStr: paymentData.nonceStr,
          package: paymentData.package,
          signType: paymentData.signType,
          paySign: paymentData.paySign,
          success: (res) => {
            showToast('支付成功', 'success')
            this.loadMembershipInfo()
            resolve(res)
          },
          fail: (err) => {
            if (err.errMsg !== 'requestPayment:fail cancel') {
              showToast('支付失败')
            }
            reject(err)
          }
        })
      })
    },
    
    // 跳转到消费记录
    goToConsumptionRecords() {
      uni.navigateTo({
        url: '/pages/member/consumption-records'
      })
    },
    
    // 跳转到会员权益
    goToMemberRights() {
      uni.navigateTo({
        url: '/pages/member/rights'
      })
    },
    
    // 联系客服
    contactService() {
      uni.showModal({
        title: '客服咨询',
        content: '请添加客服微信：kf888888',
        confirmText: '复制微信号',
        success: (res) => {
          if (res.confirm) {
            uni.setClipboardData({
              data: 'kf888888',
              success: () => {
                showToast('微信号已复制')
              }
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.member-center {
  min-height: 100vh;
  background: #f8f9fa;
  
  .member-card {
    position: relative;
    margin: 40rpx;
    border-radius: 24rpx;
    overflow: hidden;
    
    .card-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      
      image {
        width: 100%;
        height: 100%;
      }
    }
    
    .card-content {
      position: relative;
      padding: 40rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      
      .member-info {
        display: flex;
        align-items: center;
        margin-bottom: 40rpx;
        
        .avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 40rpx;
          overflow: hidden;
          margin-right: 24rpx;
          
          image {
            width: 100%;
            height: 100%;
          }
        }
        
        .user-info {
          .username {
            display: block;
            font-size: 32rpx;
            font-weight: 600;
            color: #fff;
            margin-bottom: 8rpx;
          }
          
          .member-status {
            display: block;
            font-size: 24rpx;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
      
      .member-benefits {
        display: flex;
        justify-content: space-between;
        
        .benefit-item {
          text-align: center;
          
          .benefit-label {
            display: block;
            font-size: 24rpx;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 8rpx;
          }
          
          .benefit-value {
            display: block;
            font-size: 28rpx;
            font-weight: 600;
            color: #fff;
          }
        }
      }
      
      .upgrade-tip {
        text-align: center;
        
        .tip-text {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }
  
  .membership-packages {
    margin: 40rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 32rpx;
    }
    
    .packages-list {
      display: flex;
      gap: 24rpx;
      
      .package-item {
        flex: 1;
        position: relative;
        background: #fff;
        border-radius: 16rpx;
        padding: 32rpx;
        border: 2rpx solid #e9ecef;
        
        &.recommended {
          border-color: #007aff;
          box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.15);
        }
        
        .recommended-tag {
          position: absolute;
          top: -2rpx;
          right: 32rpx;
          background: #007aff;
          color: #fff;
          font-size: 20rpx;
          padding: 8rpx 16rpx;
          border-radius: 0 0 12rpx 12rpx;
        }
        
        .package-header {
          text-align: center;
          margin-bottom: 32rpx;
          
          .package-name {
            display: block;
            font-size: 28rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 16rpx;
          }
          
          .package-price {
            .price {
              font-size: 48rpx;
              font-weight: 600;
              color: #ff4757;
            }
            
            .original-price {
              font-size: 24rpx;
              color: #999;
              text-decoration: line-through;
              margin-left: 16rpx;
            }
          }
        }
        
        .package-benefits {
          margin-bottom: 32rpx;
          
          .benefit-item {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx;
            
            .iconfont {
              font-size: 24rpx;
              color: #07c160;
              margin-right: 12rpx;
            }
            
            .benefit-text {
              font-size: 24rpx;
              color: #666;
            }
          }
        }
        
        .buy-btn {
          width: 100%;
          height: 72rpx;
          background: #f8f9fa;
          color: #666;
          border: 2rpx solid #e9ecef;
          border-radius: 36rpx;
          font-size: 28rpx;
          
          &.primary {
            background: #007aff;
            color: #fff;
            border-color: #007aff;
          }
        }
      }
    }
  }
  
  .function-entries {
    margin: 40rpx;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    
    .entry-item {
      display: flex;
      align-items: center;
      padding: 32rpx;
      border-bottom: 2rpx solid #f8f9fa;
      
      &:last-child {
        border-bottom: none;
      }
      
      .entry-icon {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f0f8ff;
        border-radius: 40rpx;
        margin-right: 24rpx;
        
        .iconfont {
          font-size: 36rpx;
          color: #007aff;
        }
      }
      
      .entry-info {
        flex: 1;
        
        .entry-title {
          display: block;
          font-size: 28rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .entry-desc {
          display: block;
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .icon-arrow-right {
        font-size: 24rpx;
        color: #ccc;
      }
    }
  }
}

.payment-modal {
  padding: 40rpx;
  
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40rpx;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .close-btn {
      font-size: 48rpx;
      color: #999;
      line-height: 1;
    }
  }
  
  .payment-info {
    margin-bottom: 40rpx;
    
    .package-info {
      text-align: center;
      margin-bottom: 24rpx;
      
      .package-name {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .package-desc {
        display: block;
        font-size: 24rpx;
        color: #666;
      }
    }
    
    .price-info {
      text-align: center;
      
      .price {
        font-size: 48rpx;
        font-weight: 600;
        color: #ff4757;
      }
      
      .original-price {
        font-size: 24rpx;
        color: #999;
        text-decoration: line-through;
        margin-left: 16rpx;
      }
    }
  }
  
  .payment-methods {
    margin-bottom: 40rpx;
    
    .method-title {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 24rpx;
    }
    
    .method-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      border: 2rpx solid #e9ecef;
      
      &.active {
        border-color: #007aff;
        background: #f0f8ff;
      }
      
      .iconfont {
        font-size: 32rpx;
        color: #07c160;
        margin-right: 16rpx;
      }
      
      .method-name {
        flex: 1;
        font-size: 28rpx;
        color: #333;
      }
      
      .icon-check {
        font-size: 24rpx;
        color: #007aff;
      }
    }
  }
  
  .confirm-pay-btn {
    width: 100%;
    height: 88rpx;
    background: #007aff;
    color: #fff;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;
  }
}
</style>
