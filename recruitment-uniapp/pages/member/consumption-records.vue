<template>
  <view class="consumption-records">
    <!-- 统计信息 -->
    <view class="stats-card">
      <view class="stats-item">
        <text class="stats-value">{{ membershipInfo.remainingViews || 0 }}</text>
        <text class="stats-label">剩余次数</text>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <text class="stats-value">{{ totalConsumption }}</text>
        <text class="stats-label">累计消费</text>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <text class="stats-value">{{ usedViews }}</text>
        <text class="stats-label">已使用次数</text>
      </view>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-tabs">
        <view v-for="tab in filterTabs" :key="tab.value" class="filter-tab"
          :class="{ active: activeFilter === tab.value }" @click="switchFilter(tab.value)">
          {{ tab.label }}
        </view>
      </view>
    </view>

    <!-- 记录列表 -->
    <scroll-view class="records-list" scroll-y @scrolltolower="loadMore" refresher-enabled
      :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
      <view v-for="record in recordsList" :key="record.id" class="record-item">
        <view class="record-icon">
          <text class="iconfont" :class="{
            'icon-recharge': record.type === 'recharge',
            'icon-consume': record.type === 'consume',
            'icon-refund': record.type === 'refund',
            'icon-vip': record.type === 'membership',
            'icon-bill': !['recharge', 'consume', 'refund', 'membership'].includes(record.type)
          }"></text>
        </view>
        <view class="record-info">
          <text class="record-title">{{ record.title }}</text>
          <text class="record-desc">{{ record.description }}</text>
          <text class="record-time">{{ formatDateTime(record.createTime) }}</text>
        </view>
        <view class="record-amount">
          <text class="amount"
            :class="{ positive: record.type === 'recharge' || record.type === 'refund', negative: record.type === 'consume' || record.type === 'membership' }">
            {{ getAmountText(record) }}
          </text>
          <text class="status"
            :class="{ success: record.status === 'success', pending: record.status === 'pending', failed: record.status === 'failed', refunded: record.status === 'refunded' }">
            {{ getStatusText(record.status) }}
          </text>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more">
        <text>{{ loading ? '加载中...' : '上拉加载更多' }}</text>
      </view>
      <view v-else-if="recordsList.length > 0" class="no-more">
        <text>没有更多记录了</text>
      </view>

      <!-- 空状态 -->
      <view v-if="!loading && recordsList.length === 0" class="empty-state">
        <image src="/static/empty-records.png" class="empty-img"></image>
        <text class="empty-text">暂无消费记录</text>
        <button class="go-member-btn" @click="goToMemberCenter">开通会员</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { paymentApi } from '@/utils/api.js'
import { formatDateTime, showToast, showLoading, hideLoading } from '@/utils/utils.js'

export default {
  name: 'ConsumptionRecords',
  data() {
    return {
      membershipInfo: {},
      recordsList: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      pageNum: 1,
      pageSize: 10,
      activeFilter: 'all',
      totalConsumption: 0,
      usedViews: 0,

      filterTabs: [
        { label: '全部', value: 'all' },
        { label: '充值', value: 'recharge' },
        { label: '消费', value: 'consume' },
        { label: '退款', value: 'refund' }
      ]
    }
  },
  onLoad() {
    this.loadMembershipInfo()
    this.loadRecords()
    this.loadStatistics()
  },
  methods: {
    formatDateTime,

    // 加载会员信息
    async loadMembershipInfo() {
      try {
        const res = await paymentApi.getMembershipInfo()
        if (res.code === 200) {
          this.membershipInfo = res.data
        }
      } catch (error) {
        console.error('加载会员信息失败:', error)
      }
    },

    // 加载消费记录
    async loadRecords(isRefresh = false) {
      if (this.loading) return

      try {
        this.loading = true
        if (isRefresh) {
          this.pageNum = 1
          this.hasMore = true
        }

        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          type: this.activeFilter === 'all' ? '' : this.activeFilter
        }

        const res = await paymentApi.getConsumptionRecords(params.pageNum, params.pageSize)

        if (res.code === 200) {
          const newList = res.data.list || []

          if (isRefresh) {
            this.recordsList = newList
          } else {
            this.recordsList = [...this.recordsList, ...newList]
          }

          this.hasMore = newList.length === this.pageSize
          this.pageNum++
        } else {
          showToast(res.msg || '加载失败')
        }
      } catch (error) {
        console.error('加载消费记录失败:', error)
        showToast('加载失败，请重试')
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    // 加载统计信息
    async loadStatistics() {
      try {
        // 这里可以调用统计接口
        // const res = await paymentApi.getConsumptionStatistics()
        // 临时使用模拟数据
        this.totalConsumption = 128
        this.usedViews = 23
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    },

    // 切换筛选
    switchFilter(value) {
      this.activeFilter = value
      this.onRefresh()
    },

    // 刷新
    onRefresh() {
      this.refreshing = true
      this.loadRecords(true)
    },

    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadRecords()
      }
    },





    // 获取金额文本
    getAmountText(record) {
      const prefix = record.type === 'recharge' || record.type === 'refund' ? '+' : '-'

      if (record.amount) {
        return `${prefix}¥${record.amount}`
      } else if (record.viewCount) {
        return `${prefix}${record.viewCount}次`
      }

      return ''
    },



    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'success': '成功',
        'pending': '处理中',
        'failed': '失败',
        'refunded': '已退款'
      }
      return textMap[status] || '未知'
    },

    // 跳转到会员中心
    goToMemberCenter() {
      uni.navigateTo({
        url: '/pages/member/center'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.consumption-records {
  min-height: 100vh;
  background: #f8f9fa;

  .stats-card {
    display: flex;
    align-items: center;
    background: #fff;
    margin: 40rpx;
    padding: 40rpx;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .stats-item {
      flex: 1;
      text-align: center;

      .stats-value {
        display: block;
        font-size: 48rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }

      .stats-label {
        display: block;
        font-size: 24rpx;
        color: #999;
      }
    }

    .stats-divider {
      width: 2rpx;
      height: 80rpx;
      background: #f0f0f0;
      margin: 0 40rpx;
    }
  }

  .filter-bar {
    background: #fff;
    padding: 0 40rpx;
    border-bottom: 2rpx solid #f0f0f0;

    .filter-tabs {
      display: flex;

      .filter-tab {
        flex: 1;
        text-align: center;
        padding: 32rpx 0;
        font-size: 28rpx;
        color: #666;
        position: relative;

        &.active {
          color: #007aff;
          font-weight: 500;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60rpx;
            height: 4rpx;
            background: #007aff;
            border-radius: 2rpx;
          }
        }
      }
    }
  }

  .records-list {
    flex: 1;
    padding: 0 40rpx;

    .record-item {
      display: flex;
      align-items: center;
      background: #fff;
      padding: 32rpx;
      margin: 24rpx 0;
      border-radius: 16rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

      .record-icon {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f0f8ff;
        border-radius: 40rpx;
        margin-right: 24rpx;

        .iconfont {
          font-size: 36rpx;
          color: #007aff;
        }
      }

      .record-info {
        flex: 1;

        .record-title {
          display: block;
          font-size: 28rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 8rpx;
        }

        .record-desc {
          display: block;
          font-size: 24rpx;
          color: #666;
          margin-bottom: 8rpx;
        }

        .record-time {
          display: block;
          font-size: 22rpx;
          color: #999;
        }
      }

      .record-amount {
        text-align: right;

        .amount {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
          margin-bottom: 8rpx;

          &.positive {
            color: #07c160;
          }

          &.negative {
            color: #ff4757;
          }
        }

        .status {
          display: block;
          font-size: 22rpx;

          &.success {
            color: #07c160;
          }

          &.pending {
            color: #ffa500;
          }

          &.failed {
            color: #ff4757;
          }

          &.refunded {
            color: #999;
          }
        }
      }
    }

    .load-more,
    .no-more {
      text-align: center;
      padding: 40rpx 0;
      font-size: 28rpx;
      color: #999;
    }

    .empty-state {
      text-align: center;
      padding: 120rpx 0;

      .empty-img {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 40rpx;
      }

      .empty-text {
        display: block;
        font-size: 28rpx;
        color: #999;
        margin-bottom: 40rpx;
      }

      .go-member-btn {
        width: 200rpx;
        height: 64rpx;
        background: #007aff;
        color: #fff;
        border: none;
        border-radius: 32rpx;
        font-size: 28rpx;
      }
    }
  }
}
</style>
