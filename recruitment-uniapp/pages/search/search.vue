<template>
  <view class="search-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-wrapper">
        <text class="iconfont icon-search"></text>
        <input class="search-input" type="text" placeholder="搜索职位、公司" v-model="keyword" @input="onInput"
          @confirm="onSearch" focus />
        <text v-if="keyword" class="clear-btn" @click="clearKeyword">×</text>
      </view>
      <text class="cancel-btn" @click="goBack">取消</text>
    </view>

    <!-- 搜索建议 -->
    <view v-if="showSuggestions && suggestions.length > 0" class="suggestions">
      <view v-for="item in suggestions" :key="item" class="suggestion-item" @click="selectSuggestion(item)">
        <text class="iconfont icon-search"></text>
        <text class="suggestion-text">{{ item }}</text>
      </view>
    </view>

    <!-- 热门搜索 -->
    <view v-if="!keyword && hotKeywords.length > 0" class="hot-keywords">
      <view class="section-title">热门搜索</view>
      <view class="keywords-list">
        <text v-for="item in hotKeywords" :key="item" class="keyword-item" @click="selectKeyword(item)">
          {{ item }}
        </text>
      </view>
    </view>

    <!-- 搜索历史 -->
    <view v-if="!keyword && searchHistory.length > 0" class="search-history">
      <view class="section-header">
        <text class="section-title">搜索历史</text>
        <text class="clear-history" @click="clearHistory">清空</text>
      </view>
      <view class="history-list">
        <view v-for="item in searchHistory" :key="item" class="history-item" @click="selectKeyword(item)">
          <text class="iconfont icon-time"></text>
          <text class="history-text">{{ item }}</text>
          <text class="remove-btn" @click.stop="removeHistory(item)">×</text>
        </view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view v-if="showResults" class="search-results">
      <view class="results-header">
        <text class="results-count">找到 {{ totalCount }} 个相关职位</text>
        <view class="sort-btn" @click="showSortPicker = true">
          <text>{{ sortText }}</text>
          <text class="iconfont icon-arrow-down"></text>
        </view>
      </view>

      <scroll-view class="results-list" scroll-y @scrolltolower="loadMore" refresher-enabled
        :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
        <view v-for="job in searchResults" :key="job.id" class="job-item" @click="goToJobDetail(job.id)">
          <view class="job-header">
            <view class="job-info">
              <text class="job-title">{{ job.title }}</text>
              <text class="salary">{{ formatSalary(job.minSalary, job.maxSalary, job.salaryType) }}</text>
            </view>
            <view class="company-logo">
              <image :src="job.companyLogo || '/static/default-company.png'" mode="aspectFill"></image>
            </view>
          </view>

          <view class="job-tags">
            <text v-for="tag in job.tags" :key="tag" class="tag">
              {{ tag }}
            </text>
          </view>

          <view class="job-meta">
            <view class="meta-item">
              <text class="iconfont icon-location"></text>
              <text>{{ job.location }}</text>
            </view>
            <view class="meta-item">
              <text class="iconfont icon-experience"></text>
              <text>{{ job.experience }}</text>
            </view>
            <view class="meta-item">
              <text class="iconfont icon-education"></text>
              <text>{{ job.education }}</text>
            </view>
          </view>

          <view class="job-footer">
            <view class="company-info">
              <text class="company-name">{{ job.companyName }}</text>
              <text class="company-scale">{{ job.companyScale }}</text>
            </view>
            <view class="publish-time">
              <text>{{ formatTime(job.publishTime) }}</text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="hasMore" class="load-more">
          <text>{{ loading ? '加载中...' : '上拉加载更多' }}</text>
        </view>
        <view v-else-if="searchResults.length > 0" class="no-more">
          <text>没有更多数据了</text>
        </view>

        <!-- 空状态 -->
        <view v-if="!loading && searchResults.length === 0 && showResults" class="empty-state">
          <image src="/static/empty-search.png" class="empty-img"></image>
          <text class="empty-text">没有找到相关职位</text>
          <text class="empty-tip">试试其他关键词吧</text>
        </view>
      </scroll-view>
    </view>

    <!-- 排序选择弹窗 -->
    <u-popup v-model="showSortPicker" mode="bottom" height="40%" border-radius="20">
      <view class="sort-picker">
        <view class="picker-header">
          <text class="title">排序方式</text>
        </view>
        <view class="sort-options">
          <view v-for="option in sortOptions" :key="option.value" class="sort-option"
            :class="{ active: sortBy === option.value }" @click="selectSort(option.value)">
            <text>{{ option.label }}</text>
            <text v-if="sortBy === option.value" class="iconfont icon-check"></text>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { jobApi } from '@/utils/api.js'
import { formatSalary, formatTime, showToast, debounce, storage } from '@/utils/utils.js'

export default {
  name: 'Search',
  data() {
    return {
      keyword: '',
      showSuggestions: false,
      suggestions: [],
      hotKeywords: [],
      searchHistory: [],
      showResults: false,
      searchResults: [],
      totalCount: 0,
      loading: false,
      refreshing: false,
      hasMore: true,
      pageNum: 1,
      pageSize: 10,
      sortBy: 'relevance',
      showSortPicker: false,

      sortOptions: [
        { label: '相关度', value: 'relevance' },
        { label: '发布时间', value: 'publishTime' },
        { label: '薪资高低', value: 'salary' }
      ]
    }
  },
  computed: {
    sortText() {
      const option = this.sortOptions.find(item => item.value === this.sortBy)
      return option ? option.label : '排序'
    }
  },
  onLoad() {
    this.loadHotKeywords()
    this.loadSearchHistory()
  },
  methods: {
    formatSalary,
    formatTime,

    // 防抖搜索
    onInput: debounce(function () {
      if (this.keyword.trim()) {
        this.getSuggestions()
      } else {
        this.showSuggestions = false
        this.showResults = false
      }
    }, 300),

    // 搜索
    onSearch() {
      if (!this.keyword.trim()) return

      this.showSuggestions = false
      this.showResults = true
      this.saveSearchHistory(this.keyword.trim())
      this.searchJobs(true)
    },

    // 获取搜索建议
    async getSuggestions() {
      try {
        // 这里可以调用搜索建议接口
        // const res = await jobApi.getSearchSuggestions(this.keyword)
        // this.suggestions = res.data || []

        // 临时使用本地数据
        this.suggestions = [
          `${this.keyword} 相关职位`,
          `${this.keyword} 公司`,
          `${this.keyword} 岗位`
        ]
        this.showSuggestions = true
      } catch (error) {
        console.error('获取搜索建议失败:', error)
      }
    },

    // 加载热门关键词
    async loadHotKeywords() {
      try {
        const res = await jobApi.getHotKeywords()
        if (res.code === 200) {
          this.hotKeywords = res.data || []
        }
      } catch (error) {
        console.error('加载热门关键词失败:', error)
        // 使用默认数据
        this.hotKeywords = ['前端开发', '后端开发', '产品经理', '设计师', '运营', '销售']
      }
    },

    // 加载搜索历史
    loadSearchHistory() {
      this.searchHistory = storage.get('searchHistory', [])
    },

    // 保存搜索历史
    saveSearchHistory(keyword) {
      let history = storage.get('searchHistory', [])

      // 移除重复项
      history = history.filter(item => item !== keyword)

      // 添加到开头
      history.unshift(keyword)

      // 限制数量
      if (history.length > 10) {
        history = history.slice(0, 10)
      }

      storage.set('searchHistory', history)
      this.searchHistory = history
    },

    // 搜索职位
    async searchJobs(isRefresh = false) {
      if (this.loading) return

      try {
        this.loading = true
        if (isRefresh) {
          this.pageNum = 1
          this.hasMore = true
        }

        const params = {
          keyword: this.keyword.trim(),
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          sortBy: this.sortBy
        }

        const res = await jobApi.searchJobs(this.keyword.trim(), params)

        if (res.code === 200) {
          const newList = res.data.list || []

          if (isRefresh) {
            this.searchResults = newList
          } else {
            this.searchResults = [...this.searchResults, ...newList]
          }

          this.totalCount = res.data.total || 0
          this.hasMore = newList.length === this.pageSize
          this.pageNum++
        } else {
          showToast(res.msg || '搜索失败')
        }
      } catch (error) {
        console.error('搜索失败:', error)
        showToast('搜索失败，请重试')
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    // 选择搜索建议
    selectSuggestion(suggestion) {
      this.keyword = suggestion
      this.onSearch()
    },

    // 选择关键词
    selectKeyword(keyword) {
      this.keyword = keyword
      this.onSearch()
    },

    // 清空关键词
    clearKeyword() {
      this.keyword = ''
      this.showSuggestions = false
      this.showResults = false
    },

    // 清空搜索历史
    clearHistory() {
      uni.showModal({
        title: '提示',
        content: '确定要清空搜索历史吗？',
        success: (res) => {
          if (res.confirm) {
            storage.remove('searchHistory')
            this.searchHistory = []
          }
        }
      })
    },

    // 删除单个历史记录
    removeHistory(keyword) {
      this.searchHistory = this.searchHistory.filter(item => item !== keyword)
      storage.set('searchHistory', this.searchHistory)
    },

    // 刷新
    onRefresh() {
      this.refreshing = true
      this.searchJobs(true)
    },

    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.searchJobs()
      }
    },

    // 选择排序方式
    selectSort(value) {
      this.sortBy = value
      this.showSortPicker = false
      this.onRefresh()
    },



    // 跳转到职位详情
    goToJobDetail(jobId) {
      uni.navigateTo({
        url: `/pages/job/detail?id=${jobId}`
      })
    },

    // 返回
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-container {
  height: 100vh;
  background: #f8f9fa;

  .search-bar {
    display: flex;
    align-items: center;
    padding: 20rpx 32rpx;
    background: #fff;
    border-bottom: 2rpx solid #f0f0f0;

    .search-input-wrapper {
      flex: 1;
      display: flex;
      align-items: center;
      height: 72rpx;
      padding: 0 24rpx;
      background: #f8f9fa;
      border-radius: 36rpx;
      margin-right: 24rpx;

      .iconfont {
        font-size: 32rpx;
        color: #999;
        margin-right: 16rpx;
      }

      .search-input {
        flex: 1;
        font-size: 28rpx;
        color: #333;
      }

      .clear-btn {
        font-size: 36rpx;
        color: #999;
        line-height: 1;
      }
    }

    .cancel-btn {
      font-size: 28rpx;
      color: #007aff;
    }
  }

  .suggestions {
    background: #fff;
    border-bottom: 2rpx solid #f0f0f0;

    .suggestion-item {
      display: flex;
      align-items: center;
      padding: 24rpx 32rpx;
      border-bottom: 2rpx solid #f8f9fa;

      .iconfont {
        font-size: 28rpx;
        color: #999;
        margin-right: 16rpx;
      }

      .suggestion-text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }

  .hot-keywords,
  .search-history {
    background: #fff;
    margin-top: 20rpx;
    padding: 32rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 24rpx;
    }

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;

      .clear-history {
        font-size: 24rpx;
        color: #999;
      }
    }

    .keywords-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .keyword-item {
        padding: 16rpx 24rpx;
        background: #f8f9fa;
        color: #666;
        font-size: 26rpx;
        border-radius: 32rpx;
        border: 2rpx solid #e9ecef;
      }
    }

    .history-list {
      .history-item {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 2rpx solid #f8f9fa;

        .iconfont {
          font-size: 28rpx;
          color: #999;
          margin-right: 16rpx;
        }

        .history-text {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }

        .remove-btn {
          font-size: 32rpx;
          color: #999;
          line-height: 1;
        }
      }
    }
  }

  .search-results {
    flex: 1;
    display: flex;
    flex-direction: column;

    .results-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 32rpx;
      background: #fff;
      border-bottom: 2rpx solid #f0f0f0;

      .results-count {
        font-size: 28rpx;
        color: #666;
      }

      .sort-btn {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #333;

        .iconfont {
          font-size: 20rpx;
          color: #999;
          margin-left: 8rpx;
        }
      }
    }

    .results-list {
      flex: 1;
      padding: 0 32rpx;

      .job-item {
        background: #fff;
        border-radius: 16rpx;
        padding: 32rpx;
        margin: 24rpx 0;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

        .job-header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: 24rpx;

          .job-info {
            flex: 1;

            .job-title {
              display: block;
              font-size: 32rpx;
              font-weight: 600;
              color: #333;
              margin-bottom: 12rpx;
            }

            .salary {
              display: block;
              font-size: 28rpx;
              color: #ff4757;
              font-weight: 600;
            }
          }

          .company-logo {
            width: 80rpx;
            height: 80rpx;
            border-radius: 12rpx;
            overflow: hidden;

            image {
              width: 100%;
              height: 100%;
            }
          }
        }

        .job-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;
          margin-bottom: 24rpx;

          .tag {
            padding: 8rpx 16rpx;
            background: #f0f8ff;
            color: #007aff;
            font-size: 24rpx;
            border-radius: 8rpx;
          }
        }

        .job-meta {
          display: flex;
          align-items: center;
          gap: 32rpx;
          margin-bottom: 24rpx;

          .meta-item {
            display: flex;
            align-items: center;
            font-size: 24rpx;
            color: #666;

            .iconfont {
              font-size: 24rpx;
              margin-right: 8rpx;
            }
          }
        }

        .job-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .company-info {
            .company-name {
              font-size: 28rpx;
              color: #333;
              margin-right: 16rpx;
            }

            .company-scale {
              font-size: 24rpx;
              color: #666;
            }
          }

          .publish-time {
            font-size: 24rpx;
            color: #999;
          }
        }
      }

      .load-more,
      .no-more {
        text-align: center;
        padding: 40rpx 0;
        font-size: 28rpx;
        color: #999;
      }

      .empty-state {
        text-align: center;
        padding: 120rpx 0;

        .empty-img {
          width: 200rpx;
          height: 200rpx;
          margin-bottom: 40rpx;
        }

        .empty-text {
          display: block;
          font-size: 28rpx;
          color: #999;
          margin-bottom: 16rpx;
        }

        .empty-tip {
          display: block;
          font-size: 24rpx;
          color: #ccc;
        }
      }
    }
  }
}

.sort-picker {
  .picker-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32rpx;
    border-bottom: 2rpx solid #f0f0f0;

    .title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .sort-options {
    .sort-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx;
      border-bottom: 2rpx solid #f8f9fa;
      font-size: 28rpx;
      color: #333;

      &.active {
        color: #007aff;
        background: #f0f8ff;
      }

      .iconfont {
        font-size: 32rpx;
        color: #007aff;
      }
    }
  }
}
</style>
