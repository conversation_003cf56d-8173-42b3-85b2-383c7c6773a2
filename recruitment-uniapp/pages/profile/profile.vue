<template>
  <view class="profile-container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info" @click="goToLogin" v-if="!isLoggedIn">
        <view class="avatar">
          <image src="/static/default-avatar.png" mode="aspectFill"></image>
        </view>
        <view class="user-details">
          <text class="username">点击登录</text>
          <text class="user-desc">登录后享受更多服务</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      
      <view class="user-info" v-else>
        <view class="avatar">
          <image :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
        </view>
        <view class="user-details">
          <text class="username">{{ userInfo.name || '用户' }}</text>
          <text class="user-desc">{{ userInfo.phone || '未绑定手机号' }}</text>
        </view>
        <view class="member-badge" v-if="membershipInfo.isMember">
          <text>会员</text>
        </view>
      </view>
      
      <!-- 统计信息 -->
      <view class="stats-info" v-if="isLoggedIn">
        <view class="stats-item" @click="goToConsumptionRecords">
          <text class="stats-value">{{ membershipInfo.remainingViews || 0 }}</text>
          <text class="stats-label">剩余次数</text>
        </view>
        <view class="stats-item" @click="goToCollections">
          <text class="stats-value">{{ collectCount }}</text>
          <text class="stats-label">收藏职位</text>
        </view>
        <view class="stats-item" @click="goToApplications">
          <text class="stats-value">{{ applicationCount }}</text>
          <text class="stats-label">投递记录</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-title">我的服务</view>
      <view class="menu-list">
        <view class="menu-item" @click="goToMemberCenter">
          <view class="menu-icon">
            <text class="iconfont icon-vip"></text>
          </view>
          <text class="menu-text">会员中心</text>
          <view class="menu-badge" v-if="!membershipInfo.isMember">
            <text>开通</text>
          </view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        
        <view class="menu-item" @click="goToConsumptionRecords">
          <view class="menu-icon">
            <text class="iconfont icon-bill"></text>
          </view>
          <text class="menu-text">消费记录</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        
        <view class="menu-item" @click="goToCollections">
          <view class="menu-icon">
            <text class="iconfont icon-heart"></text>
          </view>
          <text class="menu-text">收藏职位</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        
        <view class="menu-item" @click="goToApplications">
          <view class="menu-icon">
            <text class="iconfont icon-send"></text>
          </view>
          <text class="menu-text">投递记录</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>
    
    <!-- 工具菜单 -->
    <view class="menu-section">
      <view class="menu-title">工具</view>
      <view class="menu-list">
        <view class="menu-item" @click="goToSettings">
          <view class="menu-icon">
            <text class="iconfont icon-settings"></text>
          </view>
          <text class="menu-text">设置</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        
        <view class="menu-item" @click="contactService">
          <view class="menu-icon">
            <text class="iconfont icon-service"></text>
          </view>
          <text class="menu-text">客服咨询</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        
        <view class="menu-item" @click="showAbout">
          <view class="menu-icon">
            <text class="iconfont icon-info"></text>
          </view>
          <text class="menu-text">关于我们</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section" v-if="isLoggedIn">
      <button class="logout-btn" @click="handleLogout">退出登录</button>
    </view>
    
    <!-- 关于我们弹窗 -->
    <u-popup v-model="showAboutModal" mode="center" width="80%" border-radius="20">
      <view class="about-modal">
        <view class="modal-header">
          <text class="modal-title">关于我们</text>
          <text class="close-btn" @click="showAboutModal = false">×</text>
        </view>
        
        <view class="about-content">
          <view class="app-info">
            <image src="/static/logo.png" class="app-logo"></image>
            <text class="app-name">招聘平台</text>
            <text class="app-version">版本 1.0.0</text>
          </view>
          
          <view class="app-desc">
            <text>专业的招聘信息平台，为求职者提供优质的工作机会，为企业提供精准的人才匹配服务。</text>
          </view>
          
          <view class="contact-info">
            <view class="contact-item">
              <text class="label">客服电话：</text>
              <text class="value">400-888-8888</text>
            </view>
            <view class="contact-item">
              <text class="label">客服微信：</text>
              <text class="value">kf888888</text>
            </view>
            <view class="contact-item">
              <text class="label">官方网站：</text>
              <text class="value">www.recruitment.com</text>
            </view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { paymentApi } from '@/utils/api.js'
import { auth, showToast, showConfirm } from '@/utils/utils.js'

export default {
  name: 'Profile',
  data() {
    return {
      userInfo: {},
      membershipInfo: {
        isMember: false,
        remainingViews: 0
      },
      collectCount: 0,
      applicationCount: 0,
      showAboutModal: false
    }
  },
  computed: {
    isLoggedIn() {
      return auth.isLoggedIn()
    }
  },
  onShow() {
    if (this.isLoggedIn) {
      this.loadUserInfo()
      this.loadMembershipInfo()
      this.loadStatistics()
    }
  },
  methods: {
    // 加载用户信息
    loadUserInfo() {
      this.userInfo = auth.getUserInfo() || {}
    },
    
    // 加载会员信息
    async loadMembershipInfo() {
      try {
        const res = await paymentApi.getMembershipInfo()
        if (res.code === 200) {
          this.membershipInfo = res.data
        }
      } catch (error) {
        console.error('加载会员信息失败:', error)
      }
    },
    
    // 加载统计信息
    async loadStatistics() {
      try {
        // 这里可以调用统计接口
        // const res = await userApi.getStatistics()
        // 临时使用模拟数据
        this.collectCount = 12
        this.applicationCount = 8
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    },
    
    // 跳转到登录页
    goToLogin() {
      uni.navigateTo({
        url: '/pages/auth/login'
      })
    },
    
    // 跳转到会员中心
    goToMemberCenter() {
      if (!this.isLoggedIn) {
        this.goToLogin()
        return
      }
      uni.switchTab({
        url: '/pages/member/center'
      })
    },
    
    // 跳转到消费记录
    goToConsumptionRecords() {
      if (!this.isLoggedIn) {
        this.goToLogin()
        return
      }
      uni.navigateTo({
        url: '/pages/member/consumption-records'
      })
    },
    
    // 跳转到收藏职位
    goToCollections() {
      if (!this.isLoggedIn) {
        this.goToLogin()
        return
      }
      uni.navigateTo({
        url: '/pages/profile/collections'
      })
    },
    
    // 跳转到投递记录
    goToApplications() {
      if (!this.isLoggedIn) {
        this.goToLogin()
        return
      }
      uni.navigateTo({
        url: '/pages/profile/applications'
      })
    },
    
    // 跳转到设置
    goToSettings() {
      uni.navigateTo({
        url: '/pages/profile/settings'
      })
    },
    
    // 联系客服
    contactService() {
      uni.showModal({
        title: '客服咨询',
        content: '请添加客服微信：kf888888',
        confirmText: '复制微信号',
        success: (res) => {
          if (res.confirm) {
            uni.setClipboardData({
              data: 'kf888888',
              success: () => {
                showToast('微信号已复制')
              }
            })
          }
        }
      })
    },
    
    // 显示关于我们
    showAbout() {
      this.showAboutModal = true
    },
    
    // 退出登录
    async handleLogout() {
      const confirmed = await showConfirm('确定要退出登录吗？')
      if (confirmed) {
        auth.logout()
        showToast('已退出登录')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: #f8f9fa;
  
  .user-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 60rpx 40rpx 40rpx;
    
    .user-info {
      display: flex;
      align-items: center;
      margin-bottom: 40rpx;
      
      .avatar {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50rpx;
        overflow: hidden;
        margin-right: 24rpx;
        
        image {
          width: 100%;
          height: 100%;
        }
      }
      
      .user-details {
        flex: 1;
        
        .username {
          display: block;
          font-size: 36rpx;
          font-weight: 600;
          color: #fff;
          margin-bottom: 8rpx;
        }
        
        .user-desc {
          display: block;
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
      
      .member-badge {
        background: rgba(255, 255, 255, 0.2);
        color: #fff;
        font-size: 20rpx;
        padding: 8rpx 16rpx;
        border-radius: 16rpx;
        margin-right: 16rpx;
      }
      
      .icon-arrow-right {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
      }
    }
    
    .stats-info {
      display: flex;
      justify-content: space-around;
      
      .stats-item {
        text-align: center;
        
        .stats-value {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: #fff;
          margin-bottom: 8rpx;
        }
        
        .stats-label {
          display: block;
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
  
  .menu-section {
    margin: 40rpx;
    
    .menu-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 24rpx;
    }
    
    .menu-list {
      background: #fff;
      border-radius: 16rpx;
      overflow: hidden;
      
      .menu-item {
        display: flex;
        align-items: center;
        padding: 32rpx;
        border-bottom: 2rpx solid #f8f9fa;
        
        &:last-child {
          border-bottom: none;
        }
        
        .menu-icon {
          width: 72rpx;
          height: 72rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f0f8ff;
          border-radius: 36rpx;
          margin-right: 24rpx;
          
          .iconfont {
            font-size: 32rpx;
            color: #007aff;
          }
        }
        
        .menu-text {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }
        
        .menu-badge {
          background: #ff4757;
          color: #fff;
          font-size: 20rpx;
          padding: 6rpx 12rpx;
          border-radius: 12rpx;
          margin-right: 16rpx;
        }
        
        .icon-arrow-right {
          font-size: 24rpx;
          color: #ccc;
        }
      }
    }
  }
  
  .logout-section {
    margin: 40rpx;
    
    .logout-btn {
      width: 100%;
      height: 88rpx;
      background: #fff;
      color: #ff4757;
      border: 2rpx solid #ff4757;
      border-radius: 44rpx;
      font-size: 28rpx;
    }
  }
}

.about-modal {
  padding: 40rpx;
  
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40rpx;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .close-btn {
      font-size: 48rpx;
      color: #999;
      line-height: 1;
    }
  }
  
  .about-content {
    .app-info {
      text-align: center;
      margin-bottom: 40rpx;
      
      .app-logo {
        width: 120rpx;
        height: 120rpx;
        margin-bottom: 24rpx;
      }
      
      .app-name {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .app-version {
        display: block;
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .app-desc {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 40rpx;
    }
    
    .contact-info {
      .contact-item {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        .label {
          width: 160rpx;
          font-size: 28rpx;
          color: #666;
        }
        
        .value {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }
      }
    }
  }
}
</style>
