<template>
  <view class="settings-container">
    <!-- 账户设置 -->
    <view class="settings-section">
      <view class="section-title">账户设置</view>
      <view class="settings-list">
        <view class="setting-item" @click="editProfile">
          <view class="setting-info">
            <text class="setting-title">个人资料</text>
            <text class="setting-desc">修改头像、姓名等信息</text>
          </view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        
        <view class="setting-item" @click="changePassword">
          <view class="setting-info">
            <text class="setting-title">修改密码</text>
            <text class="setting-desc">更改登录密码</text>
          </view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        
        <view class="setting-item" @click="bindPhone">
          <view class="setting-info">
            <text class="setting-title">绑定手机</text>
            <text class="setting-desc">{{ userInfo.phone || '未绑定' }}</text>
          </view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>
    
    <!-- 通知设置 -->
    <view class="settings-section">
      <view class="section-title">通知设置</view>
      <view class="settings-list">
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">新职位推送</text>
            <text class="setting-desc">接收匹配的职位推送</text>
          </view>
          <switch :checked="settings.jobPush" @change="onJobPushChange" color="#007aff" />
        </view>
        
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-title">系统通知</text>
            <text class="setting-desc">接收系统消息通知</text>
          </view>
          <switch :checked="settings.systemNotify" @change="onSystemNotifyChange" color="#007aff" />
        </view>
      </view>
    </view>
    
    <!-- 隐私设置 -->
    <view class="settings-section">
      <view class="section-title">隐私设置</view>
      <view class="settings-list">
        <view class="setting-item" @click="showPrivacyPolicy">
          <view class="setting-info">
            <text class="setting-title">隐私政策</text>
            <text class="setting-desc">查看隐私保护政策</text>
          </view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        
        <view class="setting-item" @click="showUserAgreement">
          <view class="setting-info">
            <text class="setting-title">用户协议</text>
            <text class="setting-desc">查看用户服务协议</text>
          </view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>
    
    <!-- 其他设置 -->
    <view class="settings-section">
      <view class="section-title">其他</view>
      <view class="settings-list">
        <view class="setting-item" @click="clearCache">
          <view class="setting-info">
            <text class="setting-title">清除缓存</text>
            <text class="setting-desc">{{ cacheSize }}</text>
          </view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        
        <view class="setting-item" @click="checkUpdate">
          <view class="setting-info">
            <text class="setting-title">检查更新</text>
            <text class="setting-desc">当前版本 1.0.0</text>
          </view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        
        <view class="setting-item" @click="feedback">
          <view class="setting-info">
            <text class="setting-title">意见反馈</text>
            <text class="setting-desc">帮助我们改进产品</text>
          </view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { auth, showToast, showConfirm, storage } from '@/utils/utils.js'

export default {
  name: 'Settings',
  data() {
    return {
      userInfo: {},
      settings: {
        jobPush: true,
        systemNotify: true
      },
      cacheSize: '12.5MB'
    }
  },
  onLoad() {
    this.loadUserInfo()
    this.loadSettings()
  },
  methods: {
    // 加载用户信息
    loadUserInfo() {
      this.userInfo = auth.getUserInfo() || {}
    },
    
    // 加载设置
    loadSettings() {
      const savedSettings = storage.get('appSettings', {})
      this.settings = {
        ...this.settings,
        ...savedSettings
      }
    },
    
    // 保存设置
    saveSettings() {
      storage.set('appSettings', this.settings)
    },
    
    // 编辑个人资料
    editProfile() {
      if (!auth.isLoggedIn()) {
        uni.navigateTo({
          url: '/pages/auth/login'
        })
        return
      }
      
      uni.navigateTo({
        url: '/pages/profile/edit'
      })
    },
    
    // 修改密码
    changePassword() {
      if (!auth.isLoggedIn()) {
        uni.navigateTo({
          url: '/pages/auth/login'
        })
        return
      }
      
      uni.navigateTo({
        url: '/pages/profile/change-password'
      })
    },
    
    // 绑定手机
    bindPhone() {
      if (!auth.isLoggedIn()) {
        uni.navigateTo({
          url: '/pages/auth/login'
        })
        return
      }
      
      uni.navigateTo({
        url: '/pages/profile/bind-phone'
      })
    },
    
    // 职位推送开关变化
    onJobPushChange(e) {
      this.settings.jobPush = e.detail.value
      this.saveSettings()
      showToast(e.detail.value ? '已开启职位推送' : '已关闭职位推送')
    },
    
    // 系统通知开关变化
    onSystemNotifyChange(e) {
      this.settings.systemNotify = e.detail.value
      this.saveSettings()
      showToast(e.detail.value ? '已开启系统通知' : '已关闭系统通知')
    },
    
    // 显示隐私政策
    showPrivacyPolicy() {
      uni.navigateTo({
        url: '/pages/agreement/privacy'
      })
    },
    
    // 显示用户协议
    showUserAgreement() {
      uni.navigateTo({
        url: '/pages/agreement/user'
      })
    },
    
    // 清除缓存
    async clearCache() {
      const confirmed = await showConfirm('确定要清除缓存吗？')
      if (confirmed) {
        try {
          // 清除图片缓存等
          // uni.clearStorage() // 谨慎使用，会清除所有本地存储
          showToast('缓存清除成功')
          this.cacheSize = '0MB'
        } catch (error) {
          showToast('清除缓存失败')
        }
      }
    },
    
    // 检查更新
    checkUpdate() {
      showToast('当前已是最新版本')
    },
    
    // 意见反馈
    feedback() {
      uni.navigateTo({
        url: '/pages/profile/feedback'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-container {
  min-height: 100vh;
  background: #f8f9fa;
  
  .settings-section {
    margin: 40rpx;
    
    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 24rpx;
    }
    
    .settings-list {
      background: #fff;
      border-radius: 16rpx;
      overflow: hidden;
      
      .setting-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 32rpx;
        border-bottom: 2rpx solid #f8f9fa;
        
        &:last-child {
          border-bottom: none;
        }
        
        .setting-info {
          flex: 1;
          
          .setting-title {
            display: block;
            font-size: 28rpx;
            color: #333;
            margin-bottom: 8rpx;
          }
          
          .setting-desc {
            display: block;
            font-size: 24rpx;
            color: #999;
          }
        }
        
        .iconfont {
          font-size: 24rpx;
          color: #ccc;
        }
      }
    }
  }
}
</style>
