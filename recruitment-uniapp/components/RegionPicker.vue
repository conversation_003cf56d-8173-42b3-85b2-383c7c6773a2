<template>
  <view class="region-picker">
    <!-- 地区选择按钮 -->
    <view class="region-btn" @click="showPicker">
      <text class="region-text">{{ selectedRegionText || '选择地区' }}</text>
      <text class="iconfont icon-arrow-down"></text>
    </view>

    <!-- 地区选择弹窗 -->
    <u-popup v-model="visible" mode="bottom" height="60%" border-radius="20">
      <view class="picker-container">
        <view class="picker-header">
          <text class="cancel-btn" @click="cancel">取消</text>
          <text class="title">选择地区</text>
          <text class="confirm-btn" @click="confirm">确定</text>
        </view>

        <view class="picker-content">
          <view class="tabs">
            <view class="tab-item" :class="{ active: activeTab === 0 }" @click="switchTab(0)">
              {{ (provinces[selectedProvinceIndex] && provinces[selectedProvinceIndex].name) || '省份' }}
            </view>
            <view class="tab-item" :class="{ active: activeTab === 1 }" @click="switchTab(1)" v-if="cities.length > 0">
              {{ (cities[selectedCityIndex] && cities[selectedCityIndex].name) || '城市' }}
            </view>
            <view class="tab-item" :class="{ active: activeTab === 2 }" @click="switchTab(2)"
              v-if="districts.length > 0">
              {{ (districts[selectedDistrictIndex] && districts[selectedDistrictIndex].name) || '区县' }}
            </view>
          </view>

          <scroll-view class="list-container" scroll-y>
            <!-- 省份列表 -->
            <view v-if="activeTab === 0" class="list">
              <view v-for="(item, index) in provinces" :key="item.id" class="list-item"
                :class="{ selected: selectedProvinceIndex === index }" @click="selectProvince(index)">
                <text>{{ item.name }}</text>
                <text v-if="selectedProvinceIndex === index" class="iconfont icon-check"></text>
              </view>
            </view>

            <!-- 城市列表 -->
            <view v-if="activeTab === 1" class="list">
              <view v-for="(item, index) in cities" :key="item.id" class="list-item"
                :class="{ selected: selectedCityIndex === index }" @click="selectCity(index)">
                <text>{{ item.name }}</text>
                <text v-if="selectedCityIndex === index" class="iconfont icon-check"></text>
              </view>
            </view>

            <!-- 区县列表 -->
            <view v-if="activeTab === 2" class="list">
              <view v-for="(item, index) in districts" :key="item.id" class="list-item"
                :class="{ selected: selectedDistrictIndex === index }" @click="selectDistrict(index)">
                <text>{{ item.name }}</text>
                <text v-if="selectedDistrictIndex === index" class="iconfont icon-check"></text>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { regionApi } from '@/utils/api.js'
import { showToast, showLoading, hideLoading } from '@/utils/utils.js'

export default {
  name: 'RegionPicker',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: false,
      activeTab: 0,
      provinces: [],
      cities: [],
      districts: [],
      selectedProvinceIndex: -1,
      selectedCityIndex: -1,
      selectedDistrictIndex: -1,
      tempSelection: {
        province: null,
        city: null,
        district: null
      }
    }
  },
  computed: {
    selectedRegionText() {
      const { province, city, district } = this.value
      if (district) {
        return `${(province && province.name) || ''} ${(city && city.name) || ''} ${(district && district.name) || ''}`
      }
      if (city) {
        return `${(province && province.name) || ''} ${(city && city.name) || ''}`
      }
      if (province) {
        return province.name
      }
      return ''
    }
  },
  mounted() {
    this.loadProvinces()
    this.initSelection()
  },
  watch: {
    value: {
      handler() {
        this.initSelection()
      },
      deep: true
    }
  },
  methods: {
    // 初始化选择状态
    initSelection() {
      const { province, city, district } = this.value
      if (province) {
        const provinceIndex = this.provinces.findIndex(p => p.id === province.id)
        if (provinceIndex !== -1) {
          this.selectedProvinceIndex = provinceIndex
          this.tempSelection.province = this.provinces[provinceIndex]
        }
      }
      if (city) {
        const cityIndex = this.cities.findIndex(c => c.id === city.id)
        if (cityIndex !== -1) {
          this.selectedCityIndex = cityIndex
          this.tempSelection.city = this.cities[cityIndex]
        }
      }
      if (district) {
        const districtIndex = this.districts.findIndex(d => d.id === district.id)
        if (districtIndex !== -1) {
          this.selectedDistrictIndex = districtIndex
          this.tempSelection.district = this.districts[districtIndex]
        }
      }
    },

    // 显示选择器
    showPicker() {
      this.visible = true
      this.resetTempSelection()
    },

    // 重置临时选择
    resetTempSelection() {
      this.tempSelection = {
        province: this.value.province || null,
        city: this.value.city || null,
        district: this.value.district || null
      }
    },

    // 切换标签页
    switchTab(index) {
      this.activeTab = index
    },

    // 加载省份数据
    async loadProvinces() {
      try {
        showLoading('加载中...')
        const res = await regionApi.getProvinces()
        this.provinces = res.data || []
      } catch (error) {
        showToast('加载省份失败')
      } finally {
        hideLoading()
      }
    },

    // 加载城市数据
    async loadCities(provinceId) {
      try {
        showLoading('加载中...')
        const res = await regionApi.getCities(provinceId)
        this.cities = res.data || []
        this.districts = []
        this.selectedCityIndex = -1
        this.selectedDistrictIndex = -1
      } catch (error) {
        showToast('加载城市失败')
      } finally {
        hideLoading()
      }
    },

    // 加载区县数据
    async loadDistricts(cityId) {
      try {
        showLoading('加载中...')
        const res = await regionApi.getDistricts(cityId)
        this.districts = res.data || []
        this.selectedDistrictIndex = -1
      } catch (error) {
        showToast('加载区县失败')
      } finally {
        hideLoading()
      }
    },

    // 选择省份
    async selectProvince(index) {
      this.selectedProvinceIndex = index
      this.tempSelection.province = this.provinces[index]
      this.tempSelection.city = null
      this.tempSelection.district = null

      await this.loadCities(this.provinces[index].id)
      if (this.cities.length > 0) {
        this.activeTab = 1
      }
    },

    // 选择城市
    async selectCity(index) {
      this.selectedCityIndex = index
      this.tempSelection.city = this.cities[index]
      this.tempSelection.district = null

      await this.loadDistricts(this.cities[index].id)
      if (this.districts.length > 0) {
        this.activeTab = 2
      }
    },

    // 选择区县
    selectDistrict(index) {
      this.selectedDistrictIndex = index
      this.tempSelection.district = this.districts[index]
    },

    // 取消选择
    cancel() {
      this.visible = false
      this.activeTab = 0
    },

    // 确认选择
    confirm() {
      this.$emit('input', { ...this.tempSelection })
      this.$emit('change', { ...this.tempSelection })
      this.visible = false
      this.activeTab = 0
    }
  }
}
</script>

<style lang="scss" scoped>
.region-picker {
  .region-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 32rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    border: 2rpx solid #e9ecef;

    .region-text {
      font-size: 28rpx;
      color: #333;
    }

    .iconfont {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.picker-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;
    border-bottom: 2rpx solid #f0f0f0;

    .cancel-btn,
    .confirm-btn {
      font-size: 28rpx;
      color: #007aff;
    }

    .title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .picker-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .tabs {
      display: flex;
      border-bottom: 2rpx solid #f0f0f0;

      .tab-item {
        flex: 1;
        text-align: center;
        padding: 24rpx 0;
        font-size: 28rpx;
        color: #666;
        position: relative;

        &.active {
          color: #007aff;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60rpx;
            height: 4rpx;
            background: #007aff;
            border-radius: 2rpx;
          }
        }
      }
    }

    .list-container {
      flex: 1;

      .list {
        .list-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 32rpx;
          border-bottom: 2rpx solid #f8f9fa;
          font-size: 28rpx;
          color: #333;

          &.selected {
            color: #007aff;
            background: #f0f8ff;
          }

          .iconfont {
            font-size: 32rpx;
            color: #007aff;
          }
        }
      }
    }
  }
}
</style>
