# 微信小程序登录测试指南

## 概述
本文档描述了招聘平台小程序的微信授权登录和用户注册功能的测试流程。

## 前置条件

### 1. 微信小程序配置
- 确保微信小程序AppID已正确配置：`wx345155b124695971`
- 在微信公众平台获取并配置Secret
- 执行数据库配置更新脚本：`recruitment-api/sql/update_wechat_config.sql`

### 2. 后端服务启动
- 启动Spring Boot应用
- 确保Redis服务正常运行（用于验证码缓存）
- 确保MySQL数据库连接正常

### 3. 前端小程序配置
- 确保`manifest.json`中的appid配置正确
- 确保API请求地址配置正确

## 测试流程

### 1. 微信授权登录测试

#### 1.1 API接口测试
```bash
# 测试微信授权登录接口
POST /auth/wechat
Content-Type: application/json

{
  "code": "微信授权码"
}

# 预期响应
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "token": "生成的token",
    "userInfo": {
      "seekerId": 1,
      "openid": "用户openid",
      "phone": null,
      "name": null,
      "memberType": 1,
      "viewCount": 0,
      "status": "0",
      "registerTime": "2025-01-01T00:00:00",
      "lastLoginTime": "2025-01-01T00:00:00"
    }
  }
}
```

#### 1.2 小程序端测试步骤
1. 打开小程序登录页面
2. 点击"微信一键登录"按钮
3. 授权微信登录
4. 验证是否成功跳转到首页
5. 检查用户信息是否正确存储

### 2. 手机号验证码登录测试

#### 2.1 发送验证码
```bash
# 发送短信验证码
POST /auth/sms/send
Content-Type: application/json

{
  "phone": "13800138000"
}

# 预期响应
{
  "code": 200,
  "msg": "验证码发送成功"
}
```

#### 2.2 验证码登录
```bash
# 手机号验证码登录
POST /auth/phone
Content-Type: application/json

{
  "phone": "13800138000",
  "code": "123456"
}

# 预期响应
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "token": "生成的token",
    "userInfo": {
      "seekerId": 2,
      "openid": null,
      "phone": "13800138000",
      "name": null,
      "memberType": 1,
      "viewCount": 0,
      "status": "0",
      "registerTime": "2025-01-01T00:00:00",
      "lastLoginTime": "2025-01-01T00:00:00"
    }
  }
}
```

### 3. 用户注册测试

#### 3.1 注册接口测试
```bash
# 用户注册
POST /auth/register
Content-Type: application/json

{
  "phone": "13900139000",
  "name": "张三",
  "gender": 1,
  "age": 25,
  "education": "本科",
  "workExperience": "2年",
  "expectedSalary": "8000-12000",
  "regionId": 1
}

# 预期响应
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "token": "生成的token",
    "userInfo": {
      "seekerId": 3,
      "phone": "13900139000",
      "name": "张三",
      "gender": 1,
      "age": 25,
      "education": "本科",
      "workExperience": "2年",
      "expectedSalary": "8000-12000",
      "memberType": 1,
      "viewCount": 0,
      "status": "0",
      "registerTime": "2025-01-01T00:00:00",
      "lastLoginTime": "2025-01-01T00:00:00"
    }
  }
}
```

### 4. 微信配置管理测试

#### 4.1 获取微信配置
```bash
# 获取微信配置（需要管理员权限）
GET /recruitment/wechat/config

# 预期响应
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "appid": "wx345155b124695971",
    "secret": "****",
    "mchid": "",
    "payKey": "****"
  }
}
```

#### 4.2 更新微信配置
```bash
# 批量更新微信配置
PUT /recruitment/wechat/config/batch
Content-Type: application/json

{
  "appid": "wx345155b124695971",
  "secret": "your_wechat_secret_here"
}
```

## 测试检查点

### 1. 功能检查点
- [ ] 微信授权登录成功
- [ ] 手机号验证码发送成功
- [ ] 手机号验证码登录成功
- [ ] 用户注册成功
- [ ] Token生成和验证正常
- [ ] 用户信息正确存储和返回
- [ ] 重复登录更新最后登录时间

### 2. 安全检查点
- [ ] 验证码有效期控制（5分钟）
- [ ] 验证码发送频率限制（60秒）
- [ ] 手机号格式验证
- [ ] 重复注册检查
- [ ] Token安全性

### 3. 异常处理检查点
- [ ] 无效微信授权码处理
- [ ] 验证码错误处理
- [ ] 验证码过期处理
- [ ] 网络异常处理
- [ ] 数据库异常处理

## 常见问题排查

### 1. 微信登录失败
- 检查微信AppID和Secret配置
- 检查微信授权码是否有效
- 查看后端日志确认具体错误

### 2. 验证码发送失败
- 检查Redis连接
- 检查手机号格式
- 确认发送频率限制

### 3. 数据库连接问题
- 检查数据库连接配置
- 确认数据库表结构正确
- 检查数据库权限

## 性能测试建议

### 1. 并发测试
- 测试同时多用户登录
- 测试验证码并发发送
- 测试数据库连接池

### 2. 压力测试
- 模拟大量用户注册
- 测试Redis缓存性能
- 测试API响应时间

## 部署注意事项

### 1. 生产环境配置
- 配置真实的微信Secret
- 配置短信服务商API
- 启用HTTPS
- 配置域名白名单

### 2. 监控和日志
- 配置登录成功/失败监控
- 配置验证码发送监控
- 设置异常告警

## 总结
完成以上测试流程后，微信小程序的登录和注册功能应该能够正常工作。如有问题，请参考常见问题排查部分或查看详细的错误日志。
