<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.recruitment.mapper.RecSystemConfigMapper">
    
    <resultMap type="RecSystemConfig" id="RecSystemConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="configKey"    column="config_key"    />
        <result property="configValue"    column="config_value"    />
        <result property="configType"    column="config_type"    />
        <result property="configDesc"    column="config_desc"    />
        <result property="isSystem"    column="is_system"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRecSystemConfigVo">
        select config_id, config_key, config_value, config_type, config_desc, is_system, create_by, create_time, update_by, update_time from rec_system_config
    </sql>

    <select id="selectRecSystemConfigList" parameterType="RecSystemConfig" resultMap="RecSystemConfigResult">
        <include refid="selectRecSystemConfigVo"/>
        <where>  
            <if test="configKey != null  and configKey != ''"> and config_key like concat('%', #{configKey}, '%')</if>
            <if test="configValue != null  and configValue != ''"> and config_value like concat('%', #{configValue}, '%')</if>
            <if test="configType != null  and configType != ''"> and config_type = #{configType}</if>
            <if test="configDesc != null  and configDesc != ''"> and config_desc like concat('%', #{configDesc}, '%')</if>
            <if test="isSystem != null "> and is_system = #{isSystem}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectRecSystemConfigByConfigId" parameterType="Long" resultMap="RecSystemConfigResult">
        <include refid="selectRecSystemConfigVo"/>
        where config_id = #{configId}
    </select>

    <select id="selectRecSystemConfigByConfigKey" parameterType="String" resultMap="RecSystemConfigResult">
        <include refid="selectRecSystemConfigVo"/>
        where config_key = #{configKey}
    </select>

    <insert id="insertRecSystemConfig" parameterType="RecSystemConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into rec_system_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configKey != null and configKey != ''">config_key,</if>
            <if test="configValue != null">config_value,</if>
            <if test="configType != null">config_type,</if>
            <if test="configDesc != null">config_desc,</if>
            <if test="isSystem != null">is_system,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configKey != null and configKey != ''">#{configKey},</if>
            <if test="configValue != null">#{configValue},</if>
            <if test="configType != null">#{configType},</if>
            <if test="configDesc != null">#{configDesc},</if>
            <if test="isSystem != null">#{isSystem},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRecSystemConfig" parameterType="RecSystemConfig">
        update rec_system_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configKey != null and configKey != ''">config_key = #{configKey},</if>
            <if test="configValue != null">config_value = #{configValue},</if>
            <if test="configType != null">config_type = #{configType},</if>
            <if test="configDesc != null">config_desc = #{configDesc},</if>
            <if test="isSystem != null">is_system = #{isSystem},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <update id="updateConfigByKey" parameterType="RecSystemConfig">
        update rec_system_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configValue != null">config_value = #{configValue},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where config_key = #{configKey}
    </update>

    <delete id="deleteRecSystemConfigByConfigId" parameterType="Long">
        delete from rec_system_config where config_id = #{configId}
    </delete>

    <delete id="deleteRecSystemConfigByConfigIds" parameterType="String">
        delete from rec_system_config where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>

</mapper>
