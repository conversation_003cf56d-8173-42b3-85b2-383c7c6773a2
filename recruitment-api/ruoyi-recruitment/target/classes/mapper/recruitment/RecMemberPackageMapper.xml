<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.recruitment.mapper.RecMemberPackageMapper">
    
    <resultMap type="RecMemberPackage" id="RecMemberPackageResult">
        <result property="packageId"    column="package_id"    />
        <result property="packageName"    column="package_name"    />
        <result property="packageType"    column="package_type"    />
        <result property="price"    column="price"    />
        <result property="viewCount"    column="view_count"    />
        <result property="validityDays"    column="validity_days"    />
        <result property="description"    column="description"    />
        <result property="isActive"    column="is_active"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRecMemberPackageVo">
        select package_id, package_name, package_type, price, view_count, validity_days, description, is_active, sort_order, create_by, create_time, update_by, update_time from rec_member_package
    </sql>

    <select id="selectRecMemberPackageList" parameterType="RecMemberPackage" resultMap="RecMemberPackageResult">
        <include refid="selectRecMemberPackageVo"/>
        <where>  
            <if test="packageName != null  and packageName != ''"> and package_name like concat('%', #{packageName}, '%')</if>
            <if test="packageType != null "> and package_type = #{packageType}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectRecMemberPackageByPackageId" parameterType="Long" resultMap="RecMemberPackageResult">
        <include refid="selectRecMemberPackageVo"/>
        where package_id = #{packageId}
    </select>

    <select id="selectActivePackages" resultMap="RecMemberPackageResult">
        <include refid="selectRecMemberPackageVo"/>
        where is_active = 1
        order by sort_order asc, create_time desc
    </select>

    <select id="selectPackagesByType" parameterType="Integer" resultMap="RecMemberPackageResult">
        <include refid="selectRecMemberPackageVo"/>
        where package_type = #{packageType} and is_active = 1
        order by sort_order asc, create_time desc
    </select>

    <select id="getSingleViewPackage" resultMap="RecMemberPackageResult">
        <include refid="selectRecMemberPackageVo"/>
        where package_type = 1 and is_active = 1
        order by sort_order asc
        limit 1
    </select>

    <select id="getRecommendedPackages" resultMap="RecMemberPackageResult">
        <include refid="selectRecMemberPackageVo"/>
        where package_type = 2 and is_active = 1
        order by sort_order asc, price asc
        limit 3
    </select>

    <insert id="insertRecMemberPackage" parameterType="RecMemberPackage" useGeneratedKeys="true" keyProperty="packageId">
        insert into rec_member_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="packageName != null and packageName != ''">package_name,</if>
            <if test="packageType != null">package_type,</if>
            <if test="price != null">price,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="validityDays != null">validity_days,</if>
            <if test="description != null">description,</if>
            <if test="isActive != null">is_active,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packageName != null and packageName != ''">#{packageName},</if>
            <if test="packageType != null">#{packageType},</if>
            <if test="price != null">#{price},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="validityDays != null">#{validityDays},</if>
            <if test="description != null">#{description},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRecMemberPackage" parameterType="RecMemberPackage">
        update rec_member_package
        <trim prefix="SET" suffixOverrides=",">
            <if test="packageName != null and packageName != ''">package_name = #{packageName},</if>
            <if test="packageType != null">package_type = #{packageType},</if>
            <if test="price != null">price = #{price},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="validityDays != null">validity_days = #{validityDays},</if>
            <if test="description != null">description = #{description},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where package_id = #{packageId}
    </update>

    <delete id="deleteRecMemberPackageByPackageId" parameterType="Long">
        delete from rec_member_package where package_id = #{packageId}
    </delete>

    <delete id="deleteRecMemberPackageByPackageIds" parameterType="String">
        delete from rec_member_package where package_id in 
        <foreach item="packageId" collection="array" open="(" separator="," close=")">
            #{packageId}
        </foreach>
    </delete>

</mapper>
