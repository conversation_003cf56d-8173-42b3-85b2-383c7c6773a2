package com.ruoyi.recruitment.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.recruitment.domain.RecViewRecord;

/**
 * 查看记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IRecViewRecordService 
{
    /**
     * 查询查看记录
     * 
     * @param recordId 查看记录主键
     * @return 查看记录
     */
    public RecViewRecord selectRecViewRecordByRecordId(Long recordId);

    /**
     * 查询查看记录列表
     * 
     * @param recViewRecord 查看记录
     * @return 查看记录集合
     */
    public List<RecViewRecord> selectRecViewRecordList(RecViewRecord recViewRecord);

    /**
     * 根据求职者ID查询查看记录
     * 
     * @param seekerId 求职者ID
     * @return 查看记录集合
     */
    public List<Map<String, Object>> selectViewRecordsBySeekerId(Long seekerId);

    /**
     * 根据职位ID查询查看记录
     * 
     * @param positionId 职位ID
     * @return 查看记录集合
     */
    public List<RecViewRecord> selectViewRecordsByPositionId(Long positionId);

    /**
     * 根据商家ID查询查看记录
     * 
     * @param employerId 商家ID
     * @return 查看记录集合
     */
    public List<RecViewRecord> selectViewRecordsByEmployerId(Long employerId);

    /**
     * 新增查看记录
     * 
     * @param recViewRecord 查看记录
     * @return 结果
     */
    public int insertRecViewRecord(RecViewRecord recViewRecord);

    /**
     * 修改查看记录
     * 
     * @param recViewRecord 查看记录
     * @return 结果
     */
    public int updateRecViewRecord(RecViewRecord recViewRecord);

    /**
     * 批量删除查看记录
     * 
     * @param recordIds 需要删除的查看记录主键集合
     * @return 结果
     */
    public int deleteRecViewRecordByRecordIds(Long[] recordIds);

    /**
     * 删除查看记录信息
     * 
     * @param recordId 查看记录主键
     * @return 结果
     */
    public int deleteRecViewRecordByRecordId(Long recordId);

    /**
     * 检查用户是否已查看过该职位
     * 
     * @param seekerId 求职者ID
     * @param positionId 职位ID
     * @return 是否已查看
     */
    public boolean hasViewedPosition(Long seekerId, Long positionId);

    /**
     * 获取查看记录统计信息
     * 
     * @return 统计信息
     */
    public Object getViewRecordStatistics();

    /**
     * 获取热门职位查看统计
     * 
     * @param limit 限制数量
     * @return 统计结果
     */
    public List<Map<String, Object>> getHotPositionViewStats(Integer limit);

    /**
     * 获取用户消费统计
     * 
     * @param seekerId 求职者ID
     * @return 消费统计
     */
    public Map<String, Object> getUserConsumptionStats(Long seekerId);
}
