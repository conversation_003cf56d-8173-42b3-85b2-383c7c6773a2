package com.ruoyi.recruitment.mapper;

import java.math.BigDecimal;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.recruitment.domain.RecJobSeeker;

/**
 * 求职者Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface RecJobSeekerMapper 
{
    /**
     * 查询求职者
     * 
     * @param seekerId 求职者主键
     * @return 求职者
     */
    public RecJobSeeker selectRecJobSeekerBySeekerId(Long seekerId);

    /**
     * 根据openid查询求职者
     * 
     * @param openid 微信openid
     * @return 求职者
     */
    public RecJobSeeker selectRecJobSeekerByOpenid(String openid);

    /**
     * 根据手机号查询求职者
     * 
     * @param phone 手机号
     * @return 求职者
     */
    public RecJobSeeker selectRecJobSeekerByPhone(String phone);

    /**
     * 查询求职者列表
     * 
     * @param recJobSeeker 求职者
     * @return 求职者集合
     */
    public List<RecJobSeeker> selectRecJobSeekerList(RecJobSeeker recJobSeeker);

    /**
     * 新增求职者
     * 
     * @param recJobSeeker 求职者
     * @return 结果
     */
    public int insertRecJobSeeker(RecJobSeeker recJobSeeker);

    /**
     * 修改求职者
     * 
     * @param recJobSeeker 求职者
     * @return 结果
     */
    public int updateRecJobSeeker(RecJobSeeker recJobSeeker);

    /**
     * 删除求职者
     * 
     * @param seekerId 求职者主键
     * @return 结果
     */
    public int deleteRecJobSeekerBySeekerId(Long seekerId);

    /**
     * 批量删除求职者
     * 
     * @param seekerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecJobSeekerBySeekerIds(Long[] seekerIds);

    /**
     * 更新求职者查看次数
     *
     * @param seekerId 求职者ID
     * @param viewCount 查看次数变化量（可为负数）
     * @return 结果
     */
    public int updateSeekerViewCount(@Param("seekerId") Long seekerId, @Param("viewCount") Integer viewCount);

    /**
     * 更新求职者会员信息
     *
     * @param recJobSeeker 求职者信息
     * @return 结果
     */
    public int updateSeekerMemberInfo(RecJobSeeker recJobSeeker);

    /**
     * 更新求职者消费金额
     *
     * @param seekerId 求职者ID
     * @param amount 消费金额
     * @return 结果
     */
    public int updateSeekerConsumption(@Param("seekerId") Long seekerId, @Param("amount") BigDecimal amount);

    /**
     * 统计求职者总数
     *
     * @return 总数
     */
    public int countTotalSeekers();

    /**
     * 统计会员数量
     *
     * @return 会员数量
     */
    public int countMemberSeekers();

    /**
     * 统计今日新增求职者数量
     *
     * @return 今日新增数量
     */
    public int countTodayNewSeekers();
}
