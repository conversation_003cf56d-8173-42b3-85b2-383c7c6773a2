package com.ruoyi.recruitment.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 订单对象 rec_order
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class RecOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    private Long orderId;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderNo;

    /** 求职者ID */
    @Excel(name = "求职者ID")
    private Long seekerId;

    /** 套餐ID */
    @Excel(name = "套餐ID")
    private Long packageId;

    /** 订单类型（1单次查看 2会员套餐） */
    @Excel(name = "订单类型", readConverterExp = "1=单次查看,2=会员套餐")
    private Integer orderType;

    /** 订单金额 */
    @Excel(name = "订单金额")
    private BigDecimal orderAmount;

    /** 实付金额 */
    @Excel(name = "实付金额")
    private BigDecimal payAmount;

    /** 订单状态（0待支付 1已支付 2已取消 3已退款） */
    @Excel(name = "订单状态", readConverterExp = "0=待支付,1=已支付,2=已取消,3=已退款")
    private Integer orderStatus;

    /** 支付状态（0未支付 1已支付 2支付失败） */
    @Excel(name = "支付状态", readConverterExp = "0=未支付,1=已支付,2=支付失败")
    private Integer payStatus;

    /** 支付方式（wechat_pay） */
    @Excel(name = "支付方式")
    private String payType;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 微信支付交易号 */
    @Excel(name = "微信支付交易号")
    private String transactionId;

    /** 获得查看次数 */
    @Excel(name = "获得查看次数")
    private Integer viewCount;

    /** 订单过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 求职者昵称（非数据库字段） */
    private String seekerNickname;

    /** 套餐名称（非数据库字段） */
    private String packageName;

    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }
    public void setSeekerId(Long seekerId) 
    {
        this.seekerId = seekerId;
    }

    public Long getSeekerId() 
    {
        return seekerId;
    }
    public void setPackageId(Long packageId) 
    {
        this.packageId = packageId;
    }

    public Long getPackageId() 
    {
        return packageId;
    }
    public void setOrderType(Integer orderType) 
    {
        this.orderType = orderType;
    }

    public Integer getOrderType() 
    {
        return orderType;
    }
    public void setOrderAmount(BigDecimal orderAmount) 
    {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getOrderAmount() 
    {
        return orderAmount;
    }
    public void setPayAmount(BigDecimal payAmount) 
    {
        this.payAmount = payAmount;
    }

    public BigDecimal getPayAmount() 
    {
        return payAmount;
    }
    public void setOrderStatus(Integer orderStatus) 
    {
        this.orderStatus = orderStatus;
    }

    public Integer getOrderStatus() 
    {
        return orderStatus;
    }
    public void setPayStatus(Integer payStatus) 
    {
        this.payStatus = payStatus;
    }

    public Integer getPayStatus() 
    {
        return payStatus;
    }
    public void setPayType(String payType) 
    {
        this.payType = payType;
    }

    public String getPayType() 
    {
        return payType;
    }
    public void setPayTime(Date payTime) 
    {
        this.payTime = payTime;
    }

    public Date getPayTime() 
    {
        return payTime;
    }
    public void setTransactionId(String transactionId) 
    {
        this.transactionId = transactionId;
    }

    public String getTransactionId() 
    {
        return transactionId;
    }
    public void setViewCount(Integer viewCount) 
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount() 
    {
        return viewCount;
    }
    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }
    public void setRemark(String remark) 
    {
        this.remark = remark;
    }

    public String getRemark() 
    {
        return remark;
    }

    public String getSeekerNickname() 
    {
        return seekerNickname;
    }

    public void setSeekerNickname(String seekerNickname) 
    {
        this.seekerNickname = seekerNickname;
    }

    public String getPackageName() 
    {
        return packageName;
    }

    public void setPackageName(String packageName) 
    {
        this.packageName = packageName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("orderId", getOrderId())
            .append("orderNo", getOrderNo())
            .append("seekerId", getSeekerId())
            .append("packageId", getPackageId())
            .append("orderType", getOrderType())
            .append("orderAmount", getOrderAmount())
            .append("payAmount", getPayAmount())
            .append("orderStatus", getOrderStatus())
            .append("payStatus", getPayStatus())
            .append("payType", getPayType())
            .append("payTime", getPayTime())
            .append("transactionId", getTransactionId())
            .append("viewCount", getViewCount())
            .append("expireTime", getExpireTime())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
