package com.ruoyi.recruitment.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.recruitment.mapper.RecSystemConfigMapper;
import com.ruoyi.recruitment.domain.RecSystemConfig;
import com.ruoyi.recruitment.service.IRecSystemConfigService;

/**
 * 系统配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class RecSystemConfigServiceImpl implements IRecSystemConfigService 
{
    @Autowired
    private RecSystemConfigMapper recSystemConfigMapper;

    /**
     * 查询系统配置
     * 
     * @param configId 系统配置主键
     * @return 系统配置
     */
    @Override
    public RecSystemConfig selectRecSystemConfigByConfigId(Long configId)
    {
        return recSystemConfigMapper.selectRecSystemConfigByConfigId(configId);
    }

    /**
     * 根据配置键查询配置值
     * 
     * @param configKey 配置键
     * @return 配置值
     */
    @Override
    @Cacheable(value = "system_config", key = "#configKey")
    public String selectConfigByKey(String configKey)
    {
        RecSystemConfig config = recSystemConfigMapper.selectRecSystemConfigByConfigKey(configKey);
        return config != null ? config.getConfigValue() : null;
    }

    /**
     * 查询系统配置列表
     * 
     * @param recSystemConfig 系统配置
     * @return 系统配置
     */
    @Override
    public List<RecSystemConfig> selectRecSystemConfigList(RecSystemConfig recSystemConfig)
    {
        return recSystemConfigMapper.selectRecSystemConfigList(recSystemConfig);
    }

    /**
     * 新增系统配置
     * 
     * @param recSystemConfig 系统配置
     * @return 结果
     */
    @Override
    public int insertRecSystemConfig(RecSystemConfig recSystemConfig)
    {
        recSystemConfig.setCreateTime(DateUtils.getNowDate());
        return recSystemConfigMapper.insertRecSystemConfig(recSystemConfig);
    }

    /**
     * 修改系统配置
     * 
     * @param recSystemConfig 系统配置
     * @return 结果
     */
    @Override
    @CacheEvict(value = "system_config", key = "#recSystemConfig.configKey")
    public int updateRecSystemConfig(RecSystemConfig recSystemConfig)
    {
        recSystemConfig.setUpdateTime(DateUtils.getNowDate());
        return recSystemConfigMapper.updateRecSystemConfig(recSystemConfig);
    }

    /**
     * 批量删除系统配置
     * 
     * @param configIds 需要删除的系统配置主键
     * @return 结果
     */
    @Override
    @CacheEvict(value = "system_config", allEntries = true)
    public int deleteRecSystemConfigByConfigIds(Long[] configIds)
    {
        return recSystemConfigMapper.deleteRecSystemConfigByConfigIds(configIds);
    }

    /**
     * 删除系统配置信息
     * 
     * @param configId 系统配置主键
     * @return 结果
     */
    @Override
    @CacheEvict(value = "system_config", allEntries = true)
    public int deleteRecSystemConfigByConfigId(Long configId)
    {
        return recSystemConfigMapper.deleteRecSystemConfigByConfigId(configId);
    }

    /**
     * 根据配置键更新配置值
     * 
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 结果
     */
    @Override
    @CacheEvict(value = "system_config", key = "#configKey")
    public int updateConfigByKey(String configKey, String configValue)
    {
        RecSystemConfig config = new RecSystemConfig();
        config.setConfigKey(configKey);
        config.setConfigValue(configValue);
        config.setUpdateTime(DateUtils.getNowDate());
        return recSystemConfigMapper.updateConfigByKey(config);
    }

    /**
     * 获取微信小程序配置
     * 
     * @return 微信配置
     */
    @Override
    public RecSystemConfig getWechatConfig()
    {
        RecSystemConfig config = new RecSystemConfig();
        config.setConfigKey("wechat.appid");
        config.setConfigValue(selectConfigByKey("wechat.appid"));
        return config;
    }

    /**
     * 获取支付配置
     * 
     * @return 支付配置
     */
    @Override
    public RecSystemConfig getPaymentConfig()
    {
        RecSystemConfig config = new RecSystemConfig();
        config.setConfigKey("wechat.mch_id");
        config.setConfigValue(selectConfigByKey("wechat.mch_id"));
        return config;
    }

    /**
     * 刷新配置缓存
     */
    @Override
    @CacheEvict(value = "system_config", allEntries = true)
    public void refreshConfigCache()
    {
        // 清除缓存，下次访问时重新加载
    }
}
