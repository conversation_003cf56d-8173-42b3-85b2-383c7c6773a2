package com.ruoyi.recruitment.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 会员套餐对象 rec_member_package
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class RecMemberPackage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 套餐ID */
    private Long packageId;

    /** 套餐名称 */
    @Excel(name = "套餐名称")
    private String packageName;

    /** 套餐类型（1单次查看 2会员套餐） */
    @Excel(name = "套餐类型", readConverterExp = "1=单次查看,2=会员套餐")
    private Integer packageType;

    /** 价格 */
    @Excel(name = "价格")
    private BigDecimal price;

    /** 查看次数（0表示无限制） */
    @Excel(name = "查看次数")
    private Integer viewCount;

    /** 有效天数（0表示永久） */
    @Excel(name = "有效天数")
    private Integer validityDays;

    /** 套餐描述 */
    @Excel(name = "套餐描述")
    private String description;

    /** 是否启用（0否 1是） */
    @Excel(name = "是否启用", readConverterExp = "0=否,1=是")
    private Integer isActive;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    public void setPackageId(Long packageId) 
    {
        this.packageId = packageId;
    }

    public Long getPackageId() 
    {
        return packageId;
    }
    public void setPackageName(String packageName) 
    {
        this.packageName = packageName;
    }

    public String getPackageName() 
    {
        return packageName;
    }
    public void setPackageType(Integer packageType) 
    {
        this.packageType = packageType;
    }

    public Integer getPackageType() 
    {
        return packageType;
    }
    public void setPrice(BigDecimal price) 
    {
        this.price = price;
    }

    public BigDecimal getPrice() 
    {
        return price;
    }
    public void setViewCount(Integer viewCount) 
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount() 
    {
        return viewCount;
    }
    public void setValidityDays(Integer validityDays) 
    {
        this.validityDays = validityDays;
    }

    public Integer getValidityDays() 
    {
        return validityDays;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setIsActive(Integer isActive) 
    {
        this.isActive = isActive;
    }

    public Integer getIsActive() 
    {
        return isActive;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("packageId", getPackageId())
            .append("packageName", getPackageName())
            .append("packageType", getPackageType())
            .append("price", getPrice())
            .append("viewCount", getViewCount())
            .append("validityDays", getValidityDays())
            .append("description", getDescription())
            .append("isActive", getIsActive())
            .append("sortOrder", getSortOrder())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
