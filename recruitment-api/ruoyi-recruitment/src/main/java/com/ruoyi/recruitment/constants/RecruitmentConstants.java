package com.ruoyi.recruitment.constants;

/**
 * 招聘业务常量
 * 
 * <AUTHOR>
 */
public class RecruitmentConstants {
    
    /**
     * 默认单次查看价格
     */
    public static final String DEFAULT_SINGLE_VIEW_PRICE = "1.00";
    
    /**
     * 会员套餐类型
     */
    public static final Integer PACKAGE_TYPE_SINGLE = 1;
    public static final Integer PACKAGE_TYPE_MEMBER = 2;
    
    /**
     * 订单类型
     */
    public static final Integer ORDER_TYPE_SINGLE_VIEW = 1;
    public static final Integer ORDER_TYPE_MEMBER = 2;
    
    /**
     * 支付方式
     */
    public static final String PAY_TYPE_WECHAT = "wechat_pay";
    
    /**
     * 查看类型
     */
    public static final Integer VIEW_TYPE_SINGLE_PAY = 1;
    public static final Integer VIEW_TYPE_MEMBER = 2;
    
    /**
     * 地区级别
     */
    public static final Integer REGION_LEVEL_PROVINCE = 1;
    public static final Integer REGION_LEVEL_CITY = 2;
    public static final Integer REGION_LEVEL_COUNTY = 3;
    
    /**
     * 性别
     */
    public static final Integer GENDER_UNKNOWN = 0;
    public static final Integer GENDER_MALE = 1;
    public static final Integer GENDER_FEMALE = 2;
    
    /**
     * 工作性质
     */
    public static final String WORK_NATURE_FULL_TIME = "全职";
    public static final String WORK_NATURE_PART_TIME = "兼职";
    public static final String WORK_NATURE_INTERNSHIP = "实习";
    
    /**
     * 薪资单位
     */
    public static final String SALARY_UNIT_MONTH = "月";
    public static final String SALARY_UNIT_DAY = "日";
    public static final String SALARY_UNIT_HOUR = "小时";
    
    /**
     * 系统配置键
     */
    public static final String CONFIG_WECHAT_APPID = "wechat.appid";
    public static final String CONFIG_WECHAT_SECRET = "wechat.secret";
    public static final String CONFIG_WECHAT_PAY_MCHID = "wechat.pay.mchid";
    public static final String CONFIG_WECHAT_PAY_KEY = "wechat.pay.key";
    public static final String CONFIG_SINGLE_VIEW_PRICE = "single.view.price";
    public static final String CONFIG_JOB_AUDIT_AUTO = "job.audit.auto";
    public static final String CONFIG_EMPLOYER_AUDIT_AUTO = "employer.audit.auto";
    
    /**
     * 缓存键前缀
     */
    public static final String CACHE_REGION_PREFIX = "recruitment:region:";
    public static final String CACHE_CONFIG_PREFIX = "recruitment:config:";
    public static final String CACHE_MEMBER_PACKAGE_PREFIX = "recruitment:package:";
    
    /**
     * 订单号前缀
     */
    public static final String ORDER_NO_PREFIX = "REC";
    
    /**
     * 默认会员有效期（天）
     */
    public static final Integer DEFAULT_MEMBER_VALIDITY_DAYS = 30;
}
