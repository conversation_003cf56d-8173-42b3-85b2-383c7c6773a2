package com.ruoyi.recruitment.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 查看记录对象 rec_view_record
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class RecViewRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recordId;

    /** 求职者ID */
    @Excel(name = "求职者ID")
    private Long seekerId;

    /** 职位ID */
    @Excel(name = "职位ID")
    private Long positionId;

    /** 商家ID */
    @Excel(name = "商家ID")
    private Long employerId;

    /** 查看类型（1单次付费 2会员查看） */
    @Excel(name = "查看类型", readConverterExp = "1=单次付费,2=会员查看")
    private Integer viewType;

    /** 消费金额 */
    @Excel(name = "消费金额")
    private BigDecimal costAmount;

    /** 关联订单ID */
    @Excel(name = "关联订单ID")
    private Long orderId;

    /** 查看时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "查看时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date viewTime;

    /** 求职者昵称（非数据库字段） */
    private String seekerNickname;

    /** 职位标题（非数据库字段） */
    private String positionTitle;

    /** 企业名称（非数据库字段） */
    private String companyName;

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }
    public void setSeekerId(Long seekerId) 
    {
        this.seekerId = seekerId;
    }

    public Long getSeekerId() 
    {
        return seekerId;
    }
    public void setPositionId(Long positionId) 
    {
        this.positionId = positionId;
    }

    public Long getPositionId() 
    {
        return positionId;
    }
    public void setEmployerId(Long employerId) 
    {
        this.employerId = employerId;
    }

    public Long getEmployerId() 
    {
        return employerId;
    }
    public void setViewType(Integer viewType) 
    {
        this.viewType = viewType;
    }

    public Integer getViewType() 
    {
        return viewType;
    }
    public void setCostAmount(BigDecimal costAmount) 
    {
        this.costAmount = costAmount;
    }

    public BigDecimal getCostAmount() 
    {
        return costAmount;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setViewTime(Date viewTime) 
    {
        this.viewTime = viewTime;
    }

    public Date getViewTime() 
    {
        return viewTime;
    }

    public String getSeekerNickname() 
    {
        return seekerNickname;
    }

    public void setSeekerNickname(String seekerNickname) 
    {
        this.seekerNickname = seekerNickname;
    }

    public String getPositionTitle() 
    {
        return positionTitle;
    }

    public void setPositionTitle(String positionTitle) 
    {
        this.positionTitle = positionTitle;
    }

    public String getCompanyName() 
    {
        return companyName;
    }

    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("seekerId", getSeekerId())
            .append("positionId", getPositionId())
            .append("employerId", getEmployerId())
            .append("viewType", getViewType())
            .append("costAmount", getCostAmount())
            .append("orderId", getOrderId())
            .append("viewTime", getViewTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}
