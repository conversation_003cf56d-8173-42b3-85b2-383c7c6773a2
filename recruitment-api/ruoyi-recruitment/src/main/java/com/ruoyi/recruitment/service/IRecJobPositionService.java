package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.RecJobPosition;

/**
 * 职位Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IRecJobPositionService 
{
    /**
     * 查询职位
     * 
     * @param positionId 职位主键
     * @return 职位
     */
    public RecJobPosition selectRecJobPositionByPositionId(Long positionId);

    /**
     * 查询职位列表
     * 
     * @param recJobPosition 职位
     * @return 职位集合
     */
    public List<RecJobPosition> selectRecJobPositionList(RecJobPosition recJobPosition);

    /**
     * 根据商家ID查询职位列表
     * 
     * @param employerId 商家ID
     * @return 职位集合
     */
    public List<RecJobPosition> selectRecJobPositionByEmployerId(Long employerId);

    /**
     * 新增职位
     * 
     * @param recJobPosition 职位
     * @return 结果
     */
    public int insertRecJobPosition(RecJobPosition recJobPosition);

    /**
     * 修改职位
     * 
     * @param recJobPosition 职位
     * @return 结果
     */
    public int updateRecJobPosition(RecJobPosition recJobPosition);

    /**
     * 批量删除职位
     * 
     * @param positionIds 需要删除的职位主键集合
     * @return 结果
     */
    public int deleteRecJobPositionByPositionIds(Long[] positionIds);

    /**
     * 删除职位信息
     * 
     * @param positionId 职位主键
     * @return 结果
     */
    public int deleteRecJobPositionByPositionId(Long positionId);

    /**
     * 发布职位
     * 
     * @param recJobPosition 职位信息
     * @return 结果
     */
    public int publishPosition(RecJobPosition recJobPosition);

    /**
     * 审核职位
     * 
     * @param positionId 职位ID
     * @param auditStatus 审核状态
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int auditPosition(Long positionId, Integer auditStatus, String auditRemark);

    /**
     * 上线/下线职位
     * 
     * @param positionId 职位ID
     * @param publishStatus 发布状态
     * @return 结果
     */
    public int updatePositionStatus(Long positionId, Integer publishStatus);

    /**
     * 置顶/取消置顶职位
     * 
     * @param positionId 职位ID
     * @param isTop 是否置顶
     * @return 结果
     */
    public int updatePositionTop(Long positionId, Integer isTop);

    /**
     * 设置紧急职位
     * 
     * @param positionId 职位ID
     * @param isUrgent 是否紧急
     * @return 结果
     */
    public int updatePositionUrgent(Long positionId, Integer isUrgent);

    /**
     * 增加职位浏览次数
     * 
     * @param positionId 职位ID
     * @return 结果
     */
    public boolean increaseViewCount(Long positionId);

    /**
     * 增加职位申请次数
     * 
     * @param positionId 职位ID
     * @return 结果
     */
    public boolean increaseApplyCount(Long positionId);

    /**
     * 检查职位是否可以查看联系方式
     * 
     * @param positionId 职位ID
     * @return 是否可以查看
     */
    public boolean canViewContact(Long positionId);

    /**
     * 获取职位统计信息
     * 
     * @return 统计信息
     */
    public Object getPositionStatistics();

    /**
     * 查询待审核职位列表
     * 
     * @return 职位集合
     */
    public List<RecJobPosition> selectPendingAuditPositions();

    /**
     * 查询热门职位列表
     * 
     * @param limit 限制数量
     * @return 职位集合
     */
    public List<RecJobPosition> selectHotPositions(Integer limit);

    /**
     * 查询最新职位列表
     * 
     * @param limit 限制数量
     * @return 职位集合
     */
    public List<RecJobPosition> selectLatestPositions(Integer limit);

    /**
     * 根据地区查询职位列表
     * 
     * @param regionCode 地区编码
     * @return 职位集合
     */
    public List<RecJobPosition> selectPositionsByRegion(String regionCode);

    /**
     * 搜索职位
     * 
     * @param keyword 关键词
     * @param regionCode 地区编码
     * @return 职位集合
     */
    public List<RecJobPosition> searchPositions(String keyword, String regionCode);

    /**
     * 批量审核职位
     * 
     * @param positionIds 职位ID数组
     * @param auditStatus 审核状态
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int batchAuditPositions(Long[] positionIds, Integer auditStatus, String auditRemark);

    /**
     * 批量上线/下线职位
     * 
     * @param positionIds 职位ID数组
     * @param publishStatus 发布状态
     * @return 结果
     */
    public int batchUpdatePositionStatus(Long[] positionIds, Integer publishStatus);
}
