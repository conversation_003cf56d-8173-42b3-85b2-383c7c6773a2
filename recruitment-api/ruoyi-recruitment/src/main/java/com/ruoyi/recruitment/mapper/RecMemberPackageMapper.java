package com.ruoyi.recruitment.mapper;

import java.util.List;
import com.ruoyi.recruitment.domain.RecMemberPackage;

/**
 * 会员套餐Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface RecMemberPackageMapper 
{
    /**
     * 查询会员套餐
     * 
     * @param packageId 会员套餐主键
     * @return 会员套餐
     */
    public RecMemberPackage selectRecMemberPackageByPackageId(Long packageId);

    /**
     * 查询会员套餐列表
     * 
     * @param recMemberPackage 会员套餐
     * @return 会员套餐集合
     */
    public List<RecMemberPackage> selectRecMemberPackageList(RecMemberPackage recMemberPackage);

    /**
     * 查询启用的会员套餐列表
     * 
     * @return 会员套餐集合
     */
    public List<RecMemberPackage> selectActivePackages();

    /**
     * 根据套餐类型查询会员套餐
     * 
     * @param packageType 套餐类型
     * @return 会员套餐集合
     */
    public List<RecMemberPackage> selectPackagesByType(Integer packageType);

    /**
     * 新增会员套餐
     * 
     * @param recMemberPackage 会员套餐
     * @return 结果
     */
    public int insertRecMemberPackage(RecMemberPackage recMemberPackage);

    /**
     * 修改会员套餐
     * 
     * @param recMemberPackage 会员套餐
     * @return 结果
     */
    public int updateRecMemberPackage(RecMemberPackage recMemberPackage);

    /**
     * 删除会员套餐
     * 
     * @param packageId 会员套餐主键
     * @return 结果
     */
    public int deleteRecMemberPackageByPackageId(Long packageId);

    /**
     * 批量删除会员套餐
     * 
     * @param packageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecMemberPackageByPackageIds(Long[] packageIds);

    /**
     * 获取单次查看套餐
     * 
     * @return 单次查看套餐
     */
    public RecMemberPackage getSingleViewPackage();

    /**
     * 获取推荐会员套餐
     * 
     * @return 推荐套餐列表
     */
    public List<RecMemberPackage> getRecommendedPackages();
}
