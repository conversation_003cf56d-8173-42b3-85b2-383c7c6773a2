package com.ruoyi.recruitment.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.constants.RecruitmentConstants;
import com.ruoyi.recruitment.service.IRecSystemConfigService;

/**
 * 微信配置管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/recruitment/wechat/config")
public class WechatConfigController extends BaseController
{
    @Autowired
    private IRecSystemConfigService recSystemConfigService;

    /**
     * 获取微信配置
     */
    @PreAuthorize("@ss.hasPermi('recruitment:config:query')")
    @GetMapping
    public AjaxResult getWechatConfig()
    {
        Map<String, Object> config = new HashMap<>();
        config.put("appid", recSystemConfigService.selectConfigByKey(RecruitmentConstants.CONFIG_WECHAT_APPID));
        config.put("secret", maskSecret(recSystemConfigService.selectConfigByKey(RecruitmentConstants.CONFIG_WECHAT_SECRET)));
        config.put("mchid", recSystemConfigService.selectConfigByKey(RecruitmentConstants.CONFIG_WECHAT_PAY_MCHID));
        config.put("payKey", maskSecret(recSystemConfigService.selectConfigByKey(RecruitmentConstants.CONFIG_WECHAT_PAY_KEY)));
        
        return success(config);
    }

    /**
     * 更新微信AppID
     */
    @PreAuthorize("@ss.hasPermi('recruitment:config:edit')")
    @Log(title = "微信配置", businessType = BusinessType.UPDATE)
    @PutMapping("/appid")
    public AjaxResult updateAppId(@RequestBody Map<String, String> params)
    {
        String appid = params.get("appid");
        if (appid == null || appid.trim().isEmpty()) {
            return error("AppID不能为空");
        }
        
        int result = recSystemConfigService.updateConfigByKey(RecruitmentConstants.CONFIG_WECHAT_APPID, appid.trim());
        return toAjax(result);
    }

    /**
     * 更新微信Secret
     */
    @PreAuthorize("@ss.hasPermi('recruitment:config:edit')")
    @Log(title = "微信配置", businessType = BusinessType.UPDATE)
    @PutMapping("/secret")
    public AjaxResult updateSecret(@RequestBody Map<String, String> params)
    {
        String secret = params.get("secret");
        if (secret == null || secret.trim().isEmpty()) {
            return error("Secret不能为空");
        }
        
        int result = recSystemConfigService.updateConfigByKey(RecruitmentConstants.CONFIG_WECHAT_SECRET, secret.trim());
        return toAjax(result);
    }

    /**
     * 更新微信支付商户号
     */
    @PreAuthorize("@ss.hasPermi('recruitment:config:edit')")
    @Log(title = "微信配置", businessType = BusinessType.UPDATE)
    @PutMapping("/mchid")
    public AjaxResult updateMchId(@RequestBody Map<String, String> params)
    {
        String mchid = params.get("mchid");
        if (mchid == null || mchid.trim().isEmpty()) {
            return error("商户号不能为空");
        }
        
        int result = recSystemConfigService.updateConfigByKey(RecruitmentConstants.CONFIG_WECHAT_PAY_MCHID, mchid.trim());
        return toAjax(result);
    }

    /**
     * 更新微信支付密钥
     */
    @PreAuthorize("@ss.hasPermi('recruitment:config:edit')")
    @Log(title = "微信配置", businessType = BusinessType.UPDATE)
    @PutMapping("/paykey")
    public AjaxResult updatePayKey(@RequestBody Map<String, String> params)
    {
        String payKey = params.get("payKey");
        if (payKey == null || payKey.trim().isEmpty()) {
            return error("支付密钥不能为空");
        }
        
        int result = recSystemConfigService.updateConfigByKey(RecruitmentConstants.CONFIG_WECHAT_PAY_KEY, payKey.trim());
        return toAjax(result);
    }

    /**
     * 批量更新微信配置
     */
    @PreAuthorize("@ss.hasPermi('recruitment:config:edit')")
    @Log(title = "微信配置", businessType = BusinessType.UPDATE)
    @PutMapping("/batch")
    public AjaxResult updateWechatConfig(@RequestBody Map<String, String> params)
    {
        try {
            if (params.containsKey("appid") && !params.get("appid").trim().isEmpty()) {
                recSystemConfigService.updateConfigByKey(RecruitmentConstants.CONFIG_WECHAT_APPID, params.get("appid").trim());
            }
            
            if (params.containsKey("secret") && !params.get("secret").trim().isEmpty()) {
                recSystemConfigService.updateConfigByKey(RecruitmentConstants.CONFIG_WECHAT_SECRET, params.get("secret").trim());
            }
            
            if (params.containsKey("mchid") && !params.get("mchid").trim().isEmpty()) {
                recSystemConfigService.updateConfigByKey(RecruitmentConstants.CONFIG_WECHAT_PAY_MCHID, params.get("mchid").trim());
            }
            
            if (params.containsKey("payKey") && !params.get("payKey").trim().isEmpty()) {
                recSystemConfigService.updateConfigByKey(RecruitmentConstants.CONFIG_WECHAT_PAY_KEY, params.get("payKey").trim());
            }
            
            // 刷新配置缓存
            recSystemConfigService.refreshConfigCache();
            
            return success("配置更新成功");
        } catch (Exception e) {
            return error("配置更新失败：" + e.getMessage());
        }
    }

    /**
     * 测试微信配置
     */
    @PreAuthorize("@ss.hasPermi('recruitment:config:query')")
    @PostMapping("/test")
    public AjaxResult testWechatConfig()
    {
        try {
            String appid = recSystemConfigService.selectConfigByKey(RecruitmentConstants.CONFIG_WECHAT_APPID);
            String secret = recSystemConfigService.selectConfigByKey(RecruitmentConstants.CONFIG_WECHAT_SECRET);
            
            if (appid == null || appid.trim().isEmpty()) {
                return error("微信AppID未配置");
            }
            
            if (secret == null || secret.trim().isEmpty()) {
                return error("微信Secret未配置");
            }
            
            // TODO: 这里可以添加实际的微信API测试逻辑
            // 比如获取access_token来验证配置是否正确
            
            return success("微信配置测试通过");
        } catch (Exception e) {
            return error("微信配置测试失败：" + e.getMessage());
        }
    }

    /**
     * 掩码显示敏感信息
     */
    private String maskSecret(String secret) {
        if (secret == null || secret.length() <= 8) {
            return "****";
        }
        return secret.substring(0, 4) + "****" + secret.substring(secret.length() - 4);
    }
}
