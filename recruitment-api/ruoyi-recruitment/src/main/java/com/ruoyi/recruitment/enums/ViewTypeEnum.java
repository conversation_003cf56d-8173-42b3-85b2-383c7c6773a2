package com.ruoyi.recruitment.enums;

/**
 * 查看类型枚举
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public enum ViewTypeEnum 
{
    SINGLE_PAY(1, "单次付费查看"),
    MEMBER_VIEW(2, "会员查看");

    private final Integer code;
    private final String info;

    ViewTypeEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

    public static ViewTypeEnum getByCode(Integer code)
    {
        for (ViewTypeEnum type : ViewTypeEnum.values())
        {
            if (type.getCode().equals(code))
            {
                return type;
            }
        }
        return null;
    }
}
