package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.domain.RecJobSeeker;
import com.ruoyi.recruitment.service.IRecJobSeekerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 求职者Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/recruitment/seeker")
public class RecJobSeekerController extends BaseController
{
    @Autowired
    private IRecJobSeekerService recJobSeekerService;

    /**
     * 查询求职者列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecJobSeeker recJobSeeker)
    {
        startPage();
        List<RecJobSeeker> list = recJobSeekerService.selectRecJobSeekerList(recJobSeeker);
        return getDataTable(list);
    }

    /**
     * 导出求职者列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:export')")
    @Log(title = "求职者", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecJobSeeker recJobSeeker)
    {
        List<RecJobSeeker> list = recJobSeekerService.selectRecJobSeekerList(recJobSeeker);
        ExcelUtil<RecJobSeeker> util = new ExcelUtil<RecJobSeeker>(RecJobSeeker.class);
        util.exportExcel(response, list, "求职者数据");
    }

    /**
     * 获取求职者详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:query')")
    @GetMapping(value = "/{seekerId}")
    public AjaxResult getInfo(@PathVariable("seekerId") Long seekerId)
    {
        return success(recJobSeekerService.selectRecJobSeekerBySeekerId(seekerId));
    }

    /**
     * 新增求职者
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:add')")
    @Log(title = "求职者", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecJobSeeker recJobSeeker)
    {
        return toAjax(recJobSeekerService.insertRecJobSeeker(recJobSeeker));
    }

    /**
     * 修改求职者
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:edit')")
    @Log(title = "求职者", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecJobSeeker recJobSeeker)
    {
        return toAjax(recJobSeekerService.updateRecJobSeeker(recJobSeeker));
    }

    /**
     * 删除求职者
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:remove')")
    @Log(title = "求职者", businessType = BusinessType.DELETE)
	@DeleteMapping("/{seekerIds}")
    public AjaxResult remove(@PathVariable Long[] seekerIds)
    {
        return toAjax(recJobSeekerService.deleteRecJobSeekerBySeekerIds(seekerIds));
    }

    /**
     * 冻结/解冻求职者
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:edit')")
    @Log(title = "求职者状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{seekerId}/{status}")
    public AjaxResult updateStatus(@PathVariable Long seekerId, @PathVariable String status)
    {
        return toAjax(recJobSeekerService.updateSeekerStatus(seekerId, status));
    }

    /**
     * 获取求职者统计信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        return success(recJobSeekerService.getSeekerStatistics());
    }

    /**
     * 检查求职者是否为会员
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:query')")
    @GetMapping("/member/{seekerId}")
    public AjaxResult checkMember(@PathVariable Long seekerId)
    {
        boolean isMember = recJobSeekerService.isMember(seekerId);
        return success(isMember);
    }

    /**
     * 获取求职者剩余查看次数
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:query')")
    @GetMapping("/viewCount/{seekerId}")
    public AjaxResult getViewCount(@PathVariable Long seekerId)
    {
        int viewCount = recJobSeekerService.getRemainViewCount(seekerId);
        return success(viewCount);
    }

    /**
     * 手动增加查看次数
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:edit')")
    @Log(title = "增加查看次数", businessType = BusinessType.UPDATE)
    @PutMapping("/addViewCount/{seekerId}/{count}")
    public AjaxResult addViewCount(@PathVariable Long seekerId, @PathVariable Integer count)
    {
        boolean result = recJobSeekerService.addViewCount(seekerId, count);
        return result ? success() : error("操作失败");
    }

    /**
     * 手动升级会员
     */
    @PreAuthorize("@ss.hasPermi('recruitment:seeker:edit')")
    @Log(title = "升级会员", businessType = BusinessType.UPDATE)
    @PutMapping("/upgradeMember")
    public AjaxResult upgradeMember(@RequestBody RecJobSeeker recJobSeeker)
    {
        boolean result = recJobSeekerService.upgradeMember(
            recJobSeeker.getSeekerId(), 
            recJobSeeker.getMemberType(), 
            recJobSeeker.getViewCount(), 
            30 // 默认30天有效期
        );
        return result ? success() : error("操作失败");
    }
}
