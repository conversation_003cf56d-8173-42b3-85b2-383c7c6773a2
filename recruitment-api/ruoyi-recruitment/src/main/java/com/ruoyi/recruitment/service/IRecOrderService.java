package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.RecOrder;

/**
 * 订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IRecOrderService 
{
    /**
     * 查询订单
     * 
     * @param orderId 订单主键
     * @return 订单
     */
    public RecOrder selectRecOrderByOrderId(Long orderId);

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单
     */
    public RecOrder selectRecOrderByOrderNo(String orderNo);

    /**
     * 查询订单列表
     * 
     * @param recOrder 订单
     * @return 订单集合
     */
    public List<RecOrder> selectRecOrderList(RecOrder recOrder);

    /**
     * 根据求职者ID查询订单列表
     * 
     * @param seekerId 求职者ID
     * @return 订单集合
     */
    public List<RecOrder> selectOrdersBySeekerId(Long seekerId);

    /**
     * 新增订单
     * 
     * @param recOrder 订单
     * @return 结果
     */
    public int insertRecOrder(RecOrder recOrder);

    /**
     * 修改订单
     * 
     * @param recOrder 订单
     * @return 结果
     */
    public int updateRecOrder(RecOrder recOrder);

    /**
     * 批量删除订单
     * 
     * @param orderIds 需要删除的订单主键集合
     * @return 结果
     */
    public int deleteRecOrderByOrderIds(Long[] orderIds);

    /**
     * 删除订单信息
     * 
     * @param orderId 订单主键
     * @return 结果
     */
    public int deleteRecOrderByOrderId(Long orderId);

    /**
     * 更新订单支付状态
     * 
     * @param orderNo 订单号
     * @param payStatus 支付状态
     * @param transactionId 交易号
     * @return 结果
     */
    public int updateOrderPayStatus(String orderNo, Integer payStatus, String transactionId);

    /**
     * 取消订单
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    public int cancelOrder(Long orderId);

    /**
     * 退款订单
     * 
     * @param orderId 订单ID
     * @param refundReason 退款原因
     * @return 结果
     */
    public int refundOrder(Long orderId, String refundReason);

    /**
     * 获取订单统计信息
     * 
     * @return 统计信息
     */
    public Object getOrderStatistics();

    /**
     * 查询待支付订单
     * 
     * @return 订单集合
     */
    public List<RecOrder> selectPendingPaymentOrders();

    /**
     * 查询已支付订单
     * 
     * @return 订单集合
     */
    public List<RecOrder> selectPaidOrders();

    /**
     * 处理订单支付成功后的业务逻辑
     * 
     * @param orderNo 订单号
     * @return 处理结果
     */
    public boolean processOrderPaySuccess(String orderNo);

    /**
     * 检查订单是否过期
     * 
     * @param orderId 订单ID
     * @return 是否过期
     */
    public boolean isOrderExpired(Long orderId);

    /**
     * 自动取消过期订单
     * 
     * @return 取消的订单数量
     */
    public int cancelExpiredOrders();
}
