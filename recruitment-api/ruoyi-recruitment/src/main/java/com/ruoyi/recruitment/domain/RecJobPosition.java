package com.ruoyi.recruitment.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 职位对象 rec_job_position
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class RecJobPosition extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 职位ID */
    private Long positionId;

    /** 商家ID */
    @Excel(name = "商家ID")
    private Long employerId;

    /** 职位标题 */
    @Excel(name = "职位标题")
    private String positionTitle;

    /** 职位类型 */
    @Excel(name = "职位类型")
    private String positionType;

    /** 所属行业 */
    @Excel(name = "所属行业")
    private String industry;

    /** 工作性质（全职/兼职/实习） */
    @Excel(name = "工作性质")
    private String workNature;

    /** 最低薪资 */
    @Excel(name = "最低薪资")
    private BigDecimal salaryMin;

    /** 最高薪资 */
    @Excel(name = "最高薪资")
    private BigDecimal salaryMax;

    /** 薪资单位（月/日/小时） */
    @Excel(name = "薪资单位")
    private String salaryUnit;

    /** 工作经验要求 */
    @Excel(name = "工作经验要求")
    private String workExperience;

    /** 学历要求 */
    @Excel(name = "学历要求")
    private String education;

    /** 工作地址 */
    @Excel(name = "工作地址")
    private String workAddress;

    /** 工作地区编码 */
    @Excel(name = "工作地区编码")
    private String regionCode;

    /** 职位描述 */
    @Excel(name = "职位描述")
    private String jobDescription;

    /** 职位要求 */
    @Excel(name = "职位要求")
    private String jobRequirements;

    /** 福利待遇 */
    @Excel(name = "福利待遇")
    private String welfareBenefits;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 微信号 */
    @Excel(name = "微信号")
    private String contactWechat;

    /** 发布状态（0草稿 1待审核 2已发布 3已下线） */
    @Excel(name = "发布状态", readConverterExp = "0=草稿,1=待审核,2=已发布,3=已下线")
    private Integer publishStatus;

    /** 审核状态（0待审核 1已通过 2已拒绝） */
    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=已通过,2=已拒绝")
    private Integer auditStatus;

    /** 审核备注 */
    @Excel(name = "审核备注")
    private String auditRemark;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 申请次数 */
    @Excel(name = "申请次数")
    private Integer applyCount;

    /** 是否紧急（0否 1是） */
    @Excel(name = "是否紧急", readConverterExp = "0=否,1=是")
    private Integer isUrgent;

    /** 是否置顶（0否 1是） */
    @Excel(name = "是否置顶", readConverterExp = "0=否,1=是")
    private Integer isTop;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 企业名称（非数据库字段） */
    private String companyName;

    /** 地区名称（非数据库字段） */
    private String regionName;

    public void setPositionId(Long positionId) 
    {
        this.positionId = positionId;
    }

    public Long getPositionId() 
    {
        return positionId;
    }
    public void setEmployerId(Long employerId) 
    {
        this.employerId = employerId;
    }

    public Long getEmployerId() 
    {
        return employerId;
    }
    public void setPositionTitle(String positionTitle) 
    {
        this.positionTitle = positionTitle;
    }

    public String getPositionTitle() 
    {
        return positionTitle;
    }
    public void setPositionType(String positionType) 
    {
        this.positionType = positionType;
    }

    public String getPositionType() 
    {
        return positionType;
    }
    public void setIndustry(String industry) 
    {
        this.industry = industry;
    }

    public String getIndustry() 
    {
        return industry;
    }
    public void setWorkNature(String workNature) 
    {
        this.workNature = workNature;
    }

    public String getWorkNature() 
    {
        return workNature;
    }
    public void setSalaryMin(BigDecimal salaryMin) 
    {
        this.salaryMin = salaryMin;
    }

    public BigDecimal getSalaryMin() 
    {
        return salaryMin;
    }
    public void setSalaryMax(BigDecimal salaryMax) 
    {
        this.salaryMax = salaryMax;
    }

    public BigDecimal getSalaryMax() 
    {
        return salaryMax;
    }
    public void setSalaryUnit(String salaryUnit) 
    {
        this.salaryUnit = salaryUnit;
    }

    public String getSalaryUnit() 
    {
        return salaryUnit;
    }
    public void setWorkExperience(String workExperience) 
    {
        this.workExperience = workExperience;
    }

    public String getWorkExperience() 
    {
        return workExperience;
    }
    public void setEducation(String education) 
    {
        this.education = education;
    }

    public String getEducation() 
    {
        return education;
    }
    public void setWorkAddress(String workAddress) 
    {
        this.workAddress = workAddress;
    }

    public String getWorkAddress() 
    {
        return workAddress;
    }
    public void setRegionCode(String regionCode) 
    {
        this.regionCode = regionCode;
    }

    public String getRegionCode() 
    {
        return regionCode;
    }
    public void setJobDescription(String jobDescription) 
    {
        this.jobDescription = jobDescription;
    }

    public String getJobDescription() 
    {
        return jobDescription;
    }
    public void setJobRequirements(String jobRequirements) 
    {
        this.jobRequirements = jobRequirements;
    }

    public String getJobRequirements() 
    {
        return jobRequirements;
    }
    public void setWelfareBenefits(String welfareBenefits) 
    {
        this.welfareBenefits = welfareBenefits;
    }

    public String getWelfareBenefits() 
    {
        return welfareBenefits;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setContactWechat(String contactWechat) 
    {
        this.contactWechat = contactWechat;
    }

    public String getContactWechat() 
    {
        return contactWechat;
    }
    public void setPublishStatus(Integer publishStatus) 
    {
        this.publishStatus = publishStatus;
    }

    public Integer getPublishStatus() 
    {
        return publishStatus;
    }
    public void setAuditStatus(Integer auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public Integer getAuditStatus() 
    {
        return auditStatus;
    }
    public void setAuditRemark(String auditRemark) 
    {
        this.auditRemark = auditRemark;
    }

    public String getAuditRemark() 
    {
        return auditRemark;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAuditBy(String auditBy) 
    {
        this.auditBy = auditBy;
    }

    public String getAuditBy() 
    {
        return auditBy;
    }
    public void setPublishTime(Date publishTime) 
    {
        this.publishTime = publishTime;
    }

    public Date getPublishTime() 
    {
        return publishTime;
    }
    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }
    public void setViewCount(Integer viewCount) 
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount() 
    {
        return viewCount;
    }
    public void setApplyCount(Integer applyCount) 
    {
        this.applyCount = applyCount;
    }

    public Integer getApplyCount() 
    {
        return applyCount;
    }
    public void setIsUrgent(Integer isUrgent) 
    {
        this.isUrgent = isUrgent;
    }

    public Integer getIsUrgent() 
    {
        return isUrgent;
    }
    public void setIsTop(Integer isTop) 
    {
        this.isTop = isTop;
    }

    public Integer getIsTop() 
    {
        return isTop;
    }
    public void setSortOrder(Integer sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Integer getSortOrder() 
    {
        return sortOrder;
    }

    public String getCompanyName() 
    {
        return companyName;
    }

    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getRegionName() 
    {
        return regionName;
    }

    public void setRegionName(String regionName) 
    {
        this.regionName = regionName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("positionId", getPositionId())
            .append("employerId", getEmployerId())
            .append("positionTitle", getPositionTitle())
            .append("positionType", getPositionType())
            .append("industry", getIndustry())
            .append("workNature", getWorkNature())
            .append("salaryMin", getSalaryMin())
            .append("salaryMax", getSalaryMax())
            .append("salaryUnit", getSalaryUnit())
            .append("workExperience", getWorkExperience())
            .append("education", getEducation())
            .append("workAddress", getWorkAddress())
            .append("regionCode", getRegionCode())
            .append("jobDescription", getJobDescription())
            .append("jobRequirements", getJobRequirements())
            .append("welfareBenefits", getWelfareBenefits())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("contactWechat", getContactWechat())
            .append("publishStatus", getPublishStatus())
            .append("auditStatus", getAuditStatus())
            .append("auditRemark", getAuditRemark())
            .append("auditTime", getAuditTime())
            .append("auditBy", getAuditBy())
            .append("publishTime", getPublishTime())
            .append("expireTime", getExpireTime())
            .append("viewCount", getViewCount())
            .append("applyCount", getApplyCount())
            .append("isUrgent", getIsUrgent())
            .append("isTop", getIsTop())
            .append("sortOrder", getSortOrder())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
