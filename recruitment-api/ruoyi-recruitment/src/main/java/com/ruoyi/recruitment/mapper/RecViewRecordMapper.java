package com.ruoyi.recruitment.mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.recruitment.domain.RecViewRecord;

/**
 * 查看记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface RecViewRecordMapper 
{
    /**
     * 查询查看记录
     * 
     * @param recordId 查看记录主键
     * @return 查看记录
     */
    public RecViewRecord selectRecViewRecordByRecordId(Long recordId);

    /**
     * 查询查看记录列表
     * 
     * @param recViewRecord 查看记录
     * @return 查看记录集合
     */
    public List<RecViewRecord> selectRecViewRecordList(RecViewRecord recViewRecord);

    /**
     * 根据求职者ID查询查看记录
     * 
     * @param seekerId 求职者ID
     * @return 查看记录集合
     */
    public List<Map<String, Object>> selectViewRecordsBySeekerId(Long seekerId);

    /**
     * 根据职位ID查询查看记录
     * 
     * @param positionId 职位ID
     * @return 查看记录集合
     */
    public List<RecViewRecord> selectViewRecordsByPositionId(Long positionId);

    /**
     * 根据商家ID查询查看记录
     * 
     * @param employerId 商家ID
     * @return 查看记录集合
     */
    public List<RecViewRecord> selectViewRecordsByEmployerId(Long employerId);

    /**
     * 新增查看记录
     * 
     * @param recViewRecord 查看记录
     * @return 结果
     */
    public int insertRecViewRecord(RecViewRecord recViewRecord);

    /**
     * 修改查看记录
     * 
     * @param recViewRecord 查看记录
     * @return 结果
     */
    public int updateRecViewRecord(RecViewRecord recViewRecord);

    /**
     * 删除查看记录
     * 
     * @param recordId 查看记录主键
     * @return 结果
     */
    public int deleteRecViewRecordByRecordId(Long recordId);

    /**
     * 批量删除查看记录
     * 
     * @param recordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecViewRecordByRecordIds(Long[] recordIds);

    /**
     * 统计用户查看记录数量
     * 
     * @param seekerId 求职者ID
     * @param positionId 职位ID
     * @return 记录数量
     */
    public int countViewRecord(@Param("seekerId") Long seekerId, @Param("positionId") Long positionId);

    /**
     * 统计总查看次数
     * 
     * @return 总次数
     */
    public int countTotalViews();

    /**
     * 统计今日查看次数
     * 
     * @return 今日次数
     */
    public int countTodayViews();

    /**
     * 统计付费查看次数
     * 
     * @return 付费次数
     */
    public int countPaidViews();

    /**
     * 统计会员查看次数
     * 
     * @return 会员次数
     */
    public int countMemberViews();

    /**
     * 统计总收入
     * 
     * @return 总收入
     */
    public BigDecimal sumTotalRevenue();

    /**
     * 统计今日收入
     * 
     * @return 今日收入
     */
    public BigDecimal sumTodayRevenue();

    /**
     * 获取热门职位查看统计
     * 
     * @param limit 限制数量
     * @return 统计结果
     */
    public List<Map<String, Object>> getHotPositionViewStats(Integer limit);

    /**
     * 统计用户总查看次数
     * 
     * @param seekerId 求职者ID
     * @return 总次数
     */
    public int countUserTotalViews(Long seekerId);

    /**
     * 统计用户付费查看次数
     * 
     * @param seekerId 求职者ID
     * @return 付费次数
     */
    public int countUserPaidViews(Long seekerId);

    /**
     * 统计用户会员查看次数
     * 
     * @param seekerId 求职者ID
     * @return 会员次数
     */
    public int countUserMemberViews(Long seekerId);

    /**
     * 统计用户总消费
     * 
     * @param seekerId 求职者ID
     * @return 总消费
     */
    public BigDecimal sumUserTotalCost(Long seekerId);
}
