package com.ruoyi.recruitment.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 求职者对象 rec_job_seeker
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class RecJobSeeker extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 求职者ID */
    private Long seekerId;

    /** 微信openid */
    @Excel(name = "微信openid")
    private String openid;

    /** 微信unionid */
    @Excel(name = "微信unionid")
    private String unionid;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickname;

    /** 头像 */
    @Excel(name = "头像")
    private String avatar;

    /** 性别（0未知 1男 2女） */
    @Excel(name = "性别", readConverterExp = "0=未知,1=男,2=女")
    private Integer gender;

    /** 所在地区编码 */
    @Excel(name = "所在地区编码")
    private String regionCode;

    /** 会员类型（0普通 1会员30元 2会员50元） */
    @Excel(name = "会员类型", readConverterExp = "0=普通,1=会员30元,2=会员50元")
    private Integer memberType;

    /** 会员到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "会员到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date memberExpireTime;

    /** 剩余查看次数 */
    @Excel(name = "剩余查看次数")
    private Integer viewCount;

    /** 累计消费金额 */
    @Excel(name = "累计消费金额")
    private BigDecimal totalConsumed;

    /** 状态（0正常 1冻结） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=冻结")
    private String status;

    /** 注册时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "注册时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;

    /** 最后登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    /** 地区名称（非数据库字段） */
    private String regionName;

    public void setSeekerId(Long seekerId) 
    {
        this.seekerId = seekerId;
    }

    public Long getSeekerId() 
    {
        return seekerId;
    }
    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }
    public void setUnionid(String unionid) 
    {
        this.unionid = unionid;
    }

    public String getUnionid() 
    {
        return unionid;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setNickname(String nickname) 
    {
        this.nickname = nickname;
    }

    public String getNickname() 
    {
        return nickname;
    }
    public void setAvatar(String avatar) 
    {
        this.avatar = avatar;
    }

    public String getAvatar() 
    {
        return avatar;
    }
    public void setGender(Integer gender) 
    {
        this.gender = gender;
    }

    public Integer getGender() 
    {
        return gender;
    }
    public void setRegionCode(String regionCode) 
    {
        this.regionCode = regionCode;
    }

    public String getRegionCode() 
    {
        return regionCode;
    }
    public void setMemberType(Integer memberType) 
    {
        this.memberType = memberType;
    }

    public Integer getMemberType() 
    {
        return memberType;
    }
    public void setMemberExpireTime(Date memberExpireTime) 
    {
        this.memberExpireTime = memberExpireTime;
    }

    public Date getMemberExpireTime() 
    {
        return memberExpireTime;
    }
    public void setViewCount(Integer viewCount) 
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount() 
    {
        return viewCount;
    }
    public void setTotalConsumed(BigDecimal totalConsumed) 
    {
        this.totalConsumed = totalConsumed;
    }

    public BigDecimal getTotalConsumed() 
    {
        return totalConsumed;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setRegisterTime(Date registerTime) 
    {
        this.registerTime = registerTime;
    }

    public Date getRegisterTime() 
    {
        return registerTime;
    }
    public void setLastLoginTime(Date lastLoginTime) 
    {
        this.lastLoginTime = lastLoginTime;
    }

    public Date getLastLoginTime() 
    {
        return lastLoginTime;
    }

    public String getRegionName() 
    {
        return regionName;
    }

    public void setRegionName(String regionName) 
    {
        this.regionName = regionName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("seekerId", getSeekerId())
            .append("openid", getOpenid())
            .append("unionid", getUnionid())
            .append("phone", getPhone())
            .append("nickname", getNickname())
            .append("avatar", getAvatar())
            .append("gender", getGender())
            .append("regionCode", getRegionCode())
            .append("memberType", getMemberType())
            .append("memberExpireTime", getMemberExpireTime())
            .append("viewCount", getViewCount())
            .append("totalConsumed", getTotalConsumed())
            .append("status", getStatus())
            .append("registerTime", getRegisterTime())
            .append("lastLoginTime", getLastLoginTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
