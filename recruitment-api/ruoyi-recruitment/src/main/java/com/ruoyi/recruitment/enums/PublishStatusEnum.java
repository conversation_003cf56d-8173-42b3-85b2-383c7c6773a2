package com.ruoyi.recruitment.enums;

/**
 * 发布状态枚举
 * 
 * <AUTHOR>
 */
public enum PublishStatusEnum {
    
    DRAFT(0, "草稿"),
    PENDING_AUDIT(1, "待审核"),
    PUBLISHED(2, "已发布"),
    OFFLINE(3, "已下线");
    
    private final Integer code;
    private final String desc;
    
    PublishStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static PublishStatusEnum getByCode(Integer code) {
        for (PublishStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return DRAFT;
    }
}
