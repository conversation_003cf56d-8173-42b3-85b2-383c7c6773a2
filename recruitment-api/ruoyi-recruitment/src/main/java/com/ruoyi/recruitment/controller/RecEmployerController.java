package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.domain.RecEmployer;
import com.ruoyi.recruitment.service.IRecEmployerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商家Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/recruitment/employer")
public class RecEmployerController extends BaseController
{
    @Autowired
    private IRecEmployerService recEmployerService;

    /**
     * 查询商家列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:employer:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecEmployer recEmployer)
    {
        startPage();
        List<RecEmployer> list = recEmployerService.selectRecEmployerList(recEmployer);
        return getDataTable(list);
    }

    /**
     * 导出商家列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:employer:export')")
    @Log(title = "商家", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecEmployer recEmployer)
    {
        List<RecEmployer> list = recEmployerService.selectRecEmployerList(recEmployer);
        ExcelUtil<RecEmployer> util = new ExcelUtil<RecEmployer>(RecEmployer.class);
        util.exportExcel(response, list, "商家数据");
    }

    /**
     * 获取商家详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:employer:query')")
    @GetMapping(value = "/{employerId}")
    public AjaxResult getInfo(@PathVariable("employerId") Long employerId)
    {
        return success(recEmployerService.selectRecEmployerByEmployerId(employerId));
    }

    /**
     * 新增商家
     */
    @PreAuthorize("@ss.hasPermi('recruitment:employer:add')")
    @Log(title = "商家", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecEmployer recEmployer)
    {
        return toAjax(recEmployerService.insertRecEmployer(recEmployer));
    }

    /**
     * 修改商家
     */
    @PreAuthorize("@ss.hasPermi('recruitment:employer:edit')")
    @Log(title = "商家", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecEmployer recEmployer)
    {
        return toAjax(recEmployerService.updateRecEmployer(recEmployer));
    }

    /**
     * 删除商家
     */
    @PreAuthorize("@ss.hasPermi('recruitment:employer:remove')")
    @Log(title = "商家", businessType = BusinessType.DELETE)
	@DeleteMapping("/{employerIds}")
    public AjaxResult remove(@PathVariable Long[] employerIds)
    {
        return toAjax(recEmployerService.deleteRecEmployerByEmployerIds(employerIds));
    }

    /**
     * 审核商家
     */
    @PreAuthorize("@ss.hasPermi('recruitment:employer:audit')")
    @Log(title = "商家审核", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody RecEmployer recEmployer)
    {
        return toAjax(recEmployerService.auditEmployer(
            recEmployer.getEmployerId(), 
            recEmployer.getAuditStatus(), 
            recEmployer.getAuditRemark()
        ));
    }

    /**
     * 启用/禁用商家
     */
    @PreAuthorize("@ss.hasPermi('recruitment:employer:edit')")
    @Log(title = "商家状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{employerId}/{status}")
    public AjaxResult updateStatus(@PathVariable Long employerId, @PathVariable String status)
    {
        return toAjax(recEmployerService.updateEmployerStatus(employerId, status));
    }

    /**
     * 获取商家统计信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:employer:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        return success(recEmployerService.getEmployerStatistics());
    }

    /**
     * 查询待审核商家列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:employer:audit')")
    @GetMapping("/pendingAudit")
    public AjaxResult getPendingAuditList()
    {
        List<RecEmployer> list = recEmployerService.selectPendingAuditEmployers();
        return success(list);
    }

    /**
     * 检查登录账号是否存在
     */
    @GetMapping("/checkAccount/{loginAccount}")
    public AjaxResult checkLoginAccount(@PathVariable String loginAccount)
    {
        boolean exists = recEmployerService.checkLoginAccountExists(loginAccount);
        return success(!exists); // 返回true表示可用，false表示已存在
    }

    /**
     * 检查联系电话是否存在
     */
    @GetMapping("/checkPhone/{contactPhone}")
    public AjaxResult checkContactPhone(@PathVariable String contactPhone)
    {
        boolean exists = recEmployerService.checkContactPhoneExists(contactPhone);
        return success(!exists); // 返回true表示可用，false表示已存在
    }

    /**
     * 重置商家密码
     */
    @PreAuthorize("@ss.hasPermi('recruitment:employer:edit')")
    @Log(title = "重置密码", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPassword")
    public AjaxResult resetPassword(@RequestBody RecEmployer recEmployer)
    {
        return toAjax(recEmployerService.resetPassword(recEmployer.getEmployerId(), recEmployer.getLoginPassword()));
    }

    /**
     * 商家注册（公开接口）
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody RecEmployer recEmployer)
    {
        try {
            int result = recEmployerService.registerEmployer(recEmployer);
            if (result > 0) {
                return success("注册成功，请等待审核");
            } else {
                return error("注册失败");
            }
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 商家登录（公开接口）
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody RecEmployer recEmployer)
    {
        try {
            RecEmployer employer = recEmployerService.employerLogin(
                recEmployer.getLoginAccount(), 
                recEmployer.getLoginPassword()
            );
            if (employer != null) {
                // 清除密码信息
                employer.setLoginPassword(null);
                return success(employer);
            } else {
                return error("账号或密码错误");
            }
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
}
