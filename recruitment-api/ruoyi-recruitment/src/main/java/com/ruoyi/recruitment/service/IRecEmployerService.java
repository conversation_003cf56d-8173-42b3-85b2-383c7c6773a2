package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.RecEmployer;

/**
 * 商家Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IRecEmployerService 
{
    /**
     * 查询商家
     * 
     * @param employerId 商家主键
     * @return 商家
     */
    public RecEmployer selectRecEmployerByEmployerId(Long employerId);

    /**
     * 根据登录账号查询商家
     * 
     * @param loginAccount 登录账号
     * @return 商家
     */
    public RecEmployer selectRecEmployerByLoginAccount(String loginAccount);

    /**
     * 查询商家列表
     * 
     * @param recEmployer 商家
     * @return 商家集合
     */
    public List<RecEmployer> selectRecEmployerList(RecEmployer recEmployer);

    /**
     * 新增商家
     * 
     * @param recEmployer 商家
     * @return 结果
     */
    public int insertRecEmployer(RecEmployer recEmployer);

    /**
     * 修改商家
     * 
     * @param recEmployer 商家
     * @return 结果
     */
    public int updateRecEmployer(RecEmployer recEmployer);

    /**
     * 批量删除商家
     * 
     * @param employerIds 需要删除的商家主键集合
     * @return 结果
     */
    public int deleteRecEmployerByEmployerIds(Long[] employerIds);

    /**
     * 删除商家信息
     * 
     * @param employerId 商家主键
     * @return 结果
     */
    public int deleteRecEmployerByEmployerId(Long employerId);

    /**
     * 商家注册
     * 
     * @param recEmployer 商家信息
     * @return 结果
     */
    public int registerEmployer(RecEmployer recEmployer);

    /**
     * 商家登录
     * 
     * @param loginAccount 登录账号
     * @param password 密码
     * @return 商家信息
     */
    public RecEmployer employerLogin(String loginAccount, String password);

    /**
     * 更新商家基本信息
     * 
     * @param recEmployer 商家信息
     * @return 结果
     */
    public int updateEmployerProfile(RecEmployer recEmployer);

    /**
     * 审核商家
     * 
     * @param employerId 商家ID
     * @param auditStatus 审核状态
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int auditEmployer(Long employerId, Integer auditStatus, String auditRemark);

    /**
     * 启用/禁用商家
     * 
     * @param employerId 商家ID
     * @param status 状态
     * @return 结果
     */
    public int updateEmployerStatus(Long employerId, String status);

    /**
     * 检查商家是否已通过审核
     * 
     * @param employerId 商家ID
     * @return 是否通过审核
     */
    public boolean isApproved(Long employerId);

    /**
     * 检查登录账号是否存在
     * 
     * @param loginAccount 登录账号
     * @return 是否存在
     */
    public boolean checkLoginAccountExists(String loginAccount);

    /**
     * 检查联系电话是否存在
     * 
     * @param contactPhone 联系电话
     * @return 是否存在
     */
    public boolean checkContactPhoneExists(String contactPhone);

    /**
     * 获取商家统计信息
     * 
     * @return 统计信息
     */
    public Object getEmployerStatistics();

    /**
     * 查询待审核商家列表
     * 
     * @return 商家集合
     */
    public List<RecEmployer> selectPendingAuditEmployers();

    /**
     * 修改商家密码
     * 
     * @param employerId 商家ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 结果
     */
    public boolean changePassword(Long employerId, String oldPassword, String newPassword);

    /**
     * 重置商家密码
     * 
     * @param employerId 商家ID
     * @param newPassword 新密码
     * @return 结果
     */
    public int resetPassword(Long employerId, String newPassword);
}
