package com.ruoyi.recruitment.enums;

/**
 * 审核状态枚举
 * 
 * <AUTHOR>
 */
public enum AuditStatusEnum {
    
    PENDING(0, "待审核"),
    APPROVED(1, "已通过"),
    REJECTED(2, "已拒绝");
    
    private final Integer code;
    private final String desc;
    
    AuditStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static AuditStatusEnum getByCode(Integer code) {
        for (AuditStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PENDING;
    }
}
