package com.ruoyi.recruitment.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.recruitment.mapper.RecEmployerMapper;
import com.ruoyi.recruitment.domain.RecEmployer;
import com.ruoyi.recruitment.service.IRecEmployerService;
import com.ruoyi.recruitment.enums.AuditStatusEnum;

/**
 * 商家Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class RecEmployerServiceImpl implements IRecEmployerService 
{
    @Autowired
    private RecEmployerMapper recEmployerMapper;

    /**
     * 查询商家
     * 
     * @param employerId 商家主键
     * @return 商家
     */
    @Override
    public RecEmployer selectRecEmployerByEmployerId(Long employerId)
    {
        return recEmployerMapper.selectRecEmployerByEmployerId(employerId);
    }

    /**
     * 根据登录账号查询商家
     * 
     * @param loginAccount 登录账号
     * @return 商家
     */
    @Override
    public RecEmployer selectRecEmployerByLoginAccount(String loginAccount)
    {
        return recEmployerMapper.selectRecEmployerByLoginAccount(loginAccount);
    }

    /**
     * 查询商家列表
     * 
     * @param recEmployer 商家
     * @return 商家
     */
    @Override
    public List<RecEmployer> selectRecEmployerList(RecEmployer recEmployer)
    {
        return recEmployerMapper.selectRecEmployerList(recEmployer);
    }

    /**
     * 新增商家
     * 
     * @param recEmployer 商家
     * @return 结果
     */
    @Override
    public int insertRecEmployer(RecEmployer recEmployer)
    {
        recEmployer.setCreateTime(DateUtils.getNowDate());
        return recEmployerMapper.insertRecEmployer(recEmployer);
    }

    /**
     * 修改商家
     * 
     * @param recEmployer 商家
     * @return 结果
     */
    @Override
    public int updateRecEmployer(RecEmployer recEmployer)
    {
        recEmployer.setUpdateTime(DateUtils.getNowDate());
        return recEmployerMapper.updateRecEmployer(recEmployer);
    }

    /**
     * 批量删除商家
     * 
     * @param employerIds 需要删除的商家主键
     * @return 结果
     */
    @Override
    public int deleteRecEmployerByEmployerIds(Long[] employerIds)
    {
        return recEmployerMapper.deleteRecEmployerByEmployerIds(employerIds);
    }

    /**
     * 删除商家信息
     * 
     * @param employerId 商家主键
     * @return 结果
     */
    @Override
    public int deleteRecEmployerByEmployerId(Long employerId)
    {
        return recEmployerMapper.deleteRecEmployerByEmployerId(employerId);
    }

    /**
     * 商家注册
     * 
     * @param recEmployer 商家信息
     * @return 结果
     */
    @Override
    public int registerEmployer(RecEmployer recEmployer)
    {
        // 检查登录账号是否已存在
        if (checkLoginAccountExists(recEmployer.getLoginAccount())) {
            throw new RuntimeException("登录账号已存在");
        }
        
        // 检查联系电话是否已存在
        if (checkContactPhoneExists(recEmployer.getContactPhone())) {
            throw new RuntimeException("联系电话已存在");
        }
        
        // 设置默认值
        recEmployer.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        recEmployer.setStatus("0");
        recEmployer.setCreateTime(new Date());
        
        // 密码加密
        recEmployer.setLoginPassword(SecurityUtils.encryptPassword(recEmployer.getLoginPassword()));
        
        return recEmployerMapper.insertRecEmployer(recEmployer);
    }

    /**
     * 商家登录
     * 
     * @param loginAccount 登录账号
     * @param password 密码
     * @return 商家信息
     */
    @Override
    public RecEmployer employerLogin(String loginAccount, String password)
    {
        RecEmployer employer = recEmployerMapper.selectRecEmployerByLoginAccount(loginAccount);
        if (employer == null) {
            return null;
        }
        
        // 验证密码
        if (!SecurityUtils.matchesPassword(password, employer.getLoginPassword())) {
            return null;
        }
        
        // 检查账户状态
        if (!"0".equals(employer.getStatus())) {
            throw new RuntimeException("账户已被禁用");
        }
        
        return employer;
    }

    /**
     * 更新商家基本信息
     * 
     * @param recEmployer 商家信息
     * @return 结果
     */
    @Override
    public int updateEmployerProfile(RecEmployer recEmployer)
    {
        recEmployer.setUpdateTime(new Date());
        return recEmployerMapper.updateRecEmployer(recEmployer);
    }

    /**
     * 审核商家
     * 
     * @param employerId 商家ID
     * @param auditStatus 审核状态
     * @param auditRemark 审核备注
     * @return 结果
     */
    @Override
    public int auditEmployer(Long employerId, Integer auditStatus, String auditRemark)
    {
        RecEmployer employer = new RecEmployer();
        employer.setEmployerId(employerId);
        employer.setAuditStatus(auditStatus);
        employer.setAuditRemark(auditRemark);
        employer.setAuditTime(new Date());
        employer.setAuditBy(SecurityUtils.getUsername());
        employer.setUpdateTime(new Date());
        
        return recEmployerMapper.updateEmployerAuditStatus(employer);
    }

    /**
     * 启用/禁用商家
     * 
     * @param employerId 商家ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateEmployerStatus(Long employerId, String status)
    {
        RecEmployer employer = new RecEmployer();
        employer.setEmployerId(employerId);
        employer.setStatus(status);
        employer.setUpdateTime(new Date());
        return recEmployerMapper.updateRecEmployer(employer);
    }

    /**
     * 检查商家是否已通过审核
     * 
     * @param employerId 商家ID
     * @return 是否通过审核
     */
    @Override
    public boolean isApproved(Long employerId)
    {
        RecEmployer employer = recEmployerMapper.selectRecEmployerByEmployerId(employerId);
        if (employer == null) {
            return false;
        }
        return AuditStatusEnum.APPROVED.getCode().equals(employer.getAuditStatus());
    }

    /**
     * 检查登录账号是否存在
     * 
     * @param loginAccount 登录账号
     * @return 是否存在
     */
    @Override
    public boolean checkLoginAccountExists(String loginAccount)
    {
        RecEmployer employer = recEmployerMapper.selectRecEmployerByLoginAccount(loginAccount);
        return employer != null;
    }

    /**
     * 检查联系电话是否存在
     * 
     * @param contactPhone 联系电话
     * @return 是否存在
     */
    @Override
    public boolean checkContactPhoneExists(String contactPhone)
    {
        RecEmployer employer = recEmployerMapper.selectRecEmployerByContactPhone(contactPhone);
        return employer != null;
    }

    /**
     * 获取商家统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Object getEmployerStatistics()
    {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalEmployers", recEmployerMapper.countTotalEmployers());
        statistics.put("pendingAuditEmployers", recEmployerMapper.countPendingAuditEmployers());
        statistics.put("approvedEmployers", recEmployerMapper.countApprovedEmployers());
        statistics.put("todayNewEmployers", recEmployerMapper.countTodayNewEmployers());
        return statistics;
    }

    /**
     * 查询待审核商家列表
     * 
     * @return 商家集合
     */
    @Override
    public List<RecEmployer> selectPendingAuditEmployers()
    {
        return recEmployerMapper.selectPendingAuditEmployers();
    }

    /**
     * 修改商家密码
     * 
     * @param employerId 商家ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 结果
     */
    @Override
    public boolean changePassword(Long employerId, String oldPassword, String newPassword)
    {
        RecEmployer employer = recEmployerMapper.selectRecEmployerByEmployerId(employerId);
        if (employer == null) {
            return false;
        }
        
        // 验证旧密码
        if (!SecurityUtils.matchesPassword(oldPassword, employer.getLoginPassword())) {
            return false;
        }
        
        // 更新新密码
        employer.setLoginPassword(SecurityUtils.encryptPassword(newPassword));
        employer.setUpdateTime(new Date());
        
        int result = recEmployerMapper.updateRecEmployer(employer);
        return result > 0;
    }

    /**
     * 重置商家密码
     * 
     * @param employerId 商家ID
     * @param newPassword 新密码
     * @return 结果
     */
    @Override
    public int resetPassword(Long employerId, String newPassword)
    {
        RecEmployer employer = new RecEmployer();
        employer.setEmployerId(employerId);
        employer.setLoginPassword(SecurityUtils.encryptPassword(newPassword));
        employer.setUpdateTime(new Date());
        return recEmployerMapper.updateRecEmployer(employer);
    }
}
