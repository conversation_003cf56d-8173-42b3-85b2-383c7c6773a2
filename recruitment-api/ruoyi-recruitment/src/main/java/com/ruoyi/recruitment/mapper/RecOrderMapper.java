package com.ruoyi.recruitment.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.recruitment.domain.RecOrder;

/**
 * 订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface RecOrderMapper 
{
    /**
     * 查询订单
     * 
     * @param orderId 订单主键
     * @return 订单
     */
    public RecOrder selectRecOrderByOrderId(Long orderId);

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单
     */
    public RecOrder selectRecOrderByOrderNo(String orderNo);

    /**
     * 查询订单列表
     * 
     * @param recOrder 订单
     * @return 订单集合
     */
    public List<RecOrder> selectRecOrderList(RecOrder recOrder);

    /**
     * 根据求职者ID查询订单列表
     * 
     * @param seekerId 求职者ID
     * @return 订单集合
     */
    public List<RecOrder> selectOrdersBySeekerId(Long seekerId);

    /**
     * 新增订单
     * 
     * @param recOrder 订单
     * @return 结果
     */
    public int insertRecOrder(RecOrder recOrder);

    /**
     * 修改订单
     * 
     * @param recOrder 订单
     * @return 结果
     */
    public int updateRecOrder(RecOrder recOrder);

    /**
     * 根据订单号更新订单
     * 
     * @param recOrder 订单
     * @return 结果
     */
    public int updateOrderByOrderNo(RecOrder recOrder);

    /**
     * 删除订单
     * 
     * @param orderId 订单主键
     * @return 结果
     */
    public int deleteRecOrderByOrderId(Long orderId);

    /**
     * 批量删除订单
     * 
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecOrderByOrderIds(Long[] orderIds);

    /**
     * 统计订单总数
     * 
     * @return 总数
     */
    public int countTotalOrders();

    /**
     * 统计已支付订单数量
     * 
     * @return 已支付数量
     */
    public int countPaidOrders();

    /**
     * 统计待支付订单数量
     * 
     * @return 待支付数量
     */
    public int countPendingPaymentOrders();

    /**
     * 统计今日订单数量
     * 
     * @return 今日订单数量
     */
    public int countTodayOrders();

    /**
     * 统计订单总金额
     * 
     * @return 总金额
     */
    public BigDecimal sumTotalAmount();

    /**
     * 统计今日订单金额
     * 
     * @return 今日金额
     */
    public BigDecimal sumTodayAmount();

    /**
     * 查询待支付订单列表
     * 
     * @return 订单集合
     */
    public List<RecOrder> selectPendingPaymentOrders();

    /**
     * 查询已支付订单列表
     * 
     * @return 订单集合
     */
    public List<RecOrder> selectPaidOrders();

    /**
     * 查询过期订单列表
     * 
     * @return 订单集合
     */
    public List<RecOrder> selectExpiredOrders();
}
