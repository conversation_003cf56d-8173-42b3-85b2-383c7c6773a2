package com.ruoyi.recruitment.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 系统配置对象 rec_system_config
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class RecSystemConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long configId;

    /** 配置键 */
    @Excel(name = "配置键")
    private String configKey;

    /** 配置值 */
    @Excel(name = "配置值")
    private String configValue;

    /** 配置类型 */
    @Excel(name = "配置类型")
    private String configType;

    /** 配置描述 */
    @Excel(name = "配置描述")
    private String configDesc;

    /** 是否系统配置（0否 1是） */
    @Excel(name = "是否系统配置", readConverterExp = "0=否,1=是")
    private Integer isSystem;

    public void setConfigId(Long configId) 
    {
        this.configId = configId;
    }

    public Long getConfigId() 
    {
        return configId;
    }
    public void setConfigKey(String configKey) 
    {
        this.configKey = configKey;
    }

    public String getConfigKey() 
    {
        return configKey;
    }
    public void setConfigValue(String configValue) 
    {
        this.configValue = configValue;
    }

    public String getConfigValue() 
    {
        return configValue;
    }
    public void setConfigType(String configType) 
    {
        this.configType = configType;
    }

    public String getConfigType() 
    {
        return configType;
    }
    public void setConfigDesc(String configDesc) 
    {
        this.configDesc = configDesc;
    }

    public String getConfigDesc() 
    {
        return configDesc;
    }
    public void setIsSystem(Integer isSystem) 
    {
        this.isSystem = isSystem;
    }

    public Integer getIsSystem() 
    {
        return isSystem;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("configId", getConfigId())
            .append("configKey", getConfigKey())
            .append("configValue", getConfigValue())
            .append("configType", getConfigType())
            .append("configDesc", getConfigDesc())
            .append("isSystem", getIsSystem())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
