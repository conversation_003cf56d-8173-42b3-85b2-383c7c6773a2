package com.ruoyi.recruitment.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 商家对象 rec_employer
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class RecEmployer extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 商家ID */
    private Long employerId;

    /** 企业名称 */
    @Excel(name = "企业名称")
    private String companyName;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 营业执照号 */
    @Excel(name = "营业执照号")
    private String businessLicense;

    /** 营业执照图片 */
    @Excel(name = "营业执照图片")
    private String licenseImage;

    /** 企业地址 */
    @Excel(name = "企业地址")
    private String companyAddress;

    /** 所在地区编码 */
    @Excel(name = "所在地区编码")
    private String regionCode;

    /** 企业规模 */
    @Excel(name = "企业规模")
    private String companyScale;

    /** 企业性质 */
    @Excel(name = "企业性质")
    private String companyNature;

    /** 企业描述 */
    @Excel(name = "企业描述")
    private String companyDescription;

    /** 企业logo */
    @Excel(name = "企业logo")
    private String logoUrl;

    /** 审核状态（0待审核 1已通过 2已拒绝） */
    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=已通过,2=已拒绝")
    private Integer auditStatus;

    /** 审核备注 */
    @Excel(name = "审核备注")
    private String auditRemark;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 状态（0正常 1禁用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=禁用")
    private String status;

    /** 登录账号 */
    @Excel(name = "登录账号")
    private String loginAccount;

    /** 登录密码 */
    private String loginPassword;

    /** 地区名称（非数据库字段） */
    private String regionName;

    public void setEmployerId(Long employerId) 
    {
        this.employerId = employerId;
    }

    public Long getEmployerId() 
    {
        return employerId;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setEmail(String email) 
    {
        this.email = email;
    }

    public String getEmail() 
    {
        return email;
    }
    public void setBusinessLicense(String businessLicense) 
    {
        this.businessLicense = businessLicense;
    }

    public String getBusinessLicense() 
    {
        return businessLicense;
    }
    public void setLicenseImage(String licenseImage) 
    {
        this.licenseImage = licenseImage;
    }

    public String getLicenseImage() 
    {
        return licenseImage;
    }
    public void setCompanyAddress(String companyAddress) 
    {
        this.companyAddress = companyAddress;
    }

    public String getCompanyAddress() 
    {
        return companyAddress;
    }
    public void setRegionCode(String regionCode) 
    {
        this.regionCode = regionCode;
    }

    public String getRegionCode() 
    {
        return regionCode;
    }
    public void setCompanyScale(String companyScale) 
    {
        this.companyScale = companyScale;
    }

    public String getCompanyScale() 
    {
        return companyScale;
    }
    public void setCompanyNature(String companyNature) 
    {
        this.companyNature = companyNature;
    }

    public String getCompanyNature() 
    {
        return companyNature;
    }
    public void setCompanyDescription(String companyDescription) 
    {
        this.companyDescription = companyDescription;
    }

    public String getCompanyDescription() 
    {
        return companyDescription;
    }
    public void setLogoUrl(String logoUrl) 
    {
        this.logoUrl = logoUrl;
    }

    public String getLogoUrl() 
    {
        return logoUrl;
    }
    public void setAuditStatus(Integer auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public Integer getAuditStatus() 
    {
        return auditStatus;
    }
    public void setAuditRemark(String auditRemark) 
    {
        this.auditRemark = auditRemark;
    }

    public String getAuditRemark() 
    {
        return auditRemark;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAuditBy(String auditBy) 
    {
        this.auditBy = auditBy;
    }

    public String getAuditBy() 
    {
        return auditBy;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setLoginAccount(String loginAccount) 
    {
        this.loginAccount = loginAccount;
    }

    public String getLoginAccount() 
    {
        return loginAccount;
    }
    public void setLoginPassword(String loginPassword) 
    {
        this.loginPassword = loginPassword;
    }

    public String getLoginPassword() 
    {
        return loginPassword;
    }

    public String getRegionName() 
    {
        return regionName;
    }

    public void setRegionName(String regionName) 
    {
        this.regionName = regionName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("employerId", getEmployerId())
            .append("companyName", getCompanyName())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("email", getEmail())
            .append("businessLicense", getBusinessLicense())
            .append("licenseImage", getLicenseImage())
            .append("companyAddress", getCompanyAddress())
            .append("regionCode", getRegionCode())
            .append("companyScale", getCompanyScale())
            .append("companyNature", getCompanyNature())
            .append("companyDescription", getCompanyDescription())
            .append("logoUrl", getLogoUrl())
            .append("auditStatus", getAuditStatus())
            .append("auditRemark", getAuditRemark())
            .append("auditTime", getAuditTime())
            .append("auditBy", getAuditBy())
            .append("status", getStatus())
            .append("loginAccount", getLoginAccount())
            .append("loginPassword", getLoginPassword())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
