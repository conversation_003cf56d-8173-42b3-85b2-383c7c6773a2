package com.ruoyi.recruitment.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.recruitment.mapper.RecOrderMapper;
import com.ruoyi.recruitment.domain.RecOrder;
import com.ruoyi.recruitment.service.IRecOrderService;
import com.ruoyi.recruitment.service.IRecJobSeekerService;
import com.ruoyi.recruitment.enums.OrderStatusEnum;

/**
 * 订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class RecOrderServiceImpl implements IRecOrderService 
{
    @Autowired
    private RecOrderMapper recOrderMapper;
    
    @Autowired
    private IRecJobSeekerService recJobSeekerService;

    /**
     * 查询订单
     * 
     * @param orderId 订单主键
     * @return 订单
     */
    @Override
    public RecOrder selectRecOrderByOrderId(Long orderId)
    {
        return recOrderMapper.selectRecOrderByOrderId(orderId);
    }

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单
     */
    @Override
    public RecOrder selectRecOrderByOrderNo(String orderNo)
    {
        return recOrderMapper.selectRecOrderByOrderNo(orderNo);
    }

    /**
     * 查询订单列表
     * 
     * @param recOrder 订单
     * @return 订单
     */
    @Override
    public List<RecOrder> selectRecOrderList(RecOrder recOrder)
    {
        return recOrderMapper.selectRecOrderList(recOrder);
    }

    /**
     * 根据求职者ID查询订单列表
     * 
     * @param seekerId 求职者ID
     * @return 订单集合
     */
    @Override
    public List<RecOrder> selectOrdersBySeekerId(Long seekerId)
    {
        return recOrderMapper.selectOrdersBySeekerId(seekerId);
    }

    /**
     * 新增订单
     * 
     * @param recOrder 订单
     * @return 结果
     */
    @Override
    public int insertRecOrder(RecOrder recOrder)
    {
        recOrder.setCreateTime(DateUtils.getNowDate());
        return recOrderMapper.insertRecOrder(recOrder);
    }

    /**
     * 修改订单
     * 
     * @param recOrder 订单
     * @return 结果
     */
    @Override
    public int updateRecOrder(RecOrder recOrder)
    {
        recOrder.setUpdateTime(DateUtils.getNowDate());
        return recOrderMapper.updateRecOrder(recOrder);
    }

    /**
     * 批量删除订单
     * 
     * @param orderIds 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteRecOrderByOrderIds(Long[] orderIds)
    {
        return recOrderMapper.deleteRecOrderByOrderIds(orderIds);
    }

    /**
     * 删除订单信息
     * 
     * @param orderId 订单主键
     * @return 结果
     */
    @Override
    public int deleteRecOrderByOrderId(Long orderId)
    {
        return recOrderMapper.deleteRecOrderByOrderId(orderId);
    }

    /**
     * 更新订单支付状态
     * 
     * @param orderNo 订单号
     * @param payStatus 支付状态
     * @param transactionId 交易号
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrderPayStatus(String orderNo, Integer payStatus, String transactionId)
    {
        RecOrder order = new RecOrder();
        order.setOrderNo(orderNo);
        order.setPayStatus(payStatus);
        order.setTransactionId(transactionId);
        order.setUpdateTime(new Date());
        
        if (payStatus == 1) { // 支付成功
            order.setOrderStatus(OrderStatusEnum.PAID.getCode());
            order.setPayTime(new Date());
        }
        
        int result = recOrderMapper.updateOrderByOrderNo(order);
        
        // 如果支付成功，处理后续业务逻辑
        if (payStatus == 1 && result > 0) {
            processOrderPaySuccess(orderNo);
        }
        
        return result;
    }

    /**
     * 取消订单
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    public int cancelOrder(Long orderId)
    {
        RecOrder order = new RecOrder();
        order.setOrderId(orderId);
        order.setOrderStatus(OrderStatusEnum.CANCELLED.getCode());
        order.setUpdateTime(new Date());
        order.setUpdateBy(SecurityUtils.getUsername());
        
        return recOrderMapper.updateRecOrder(order);
    }

    /**
     * 退款订单
     * 
     * @param orderId 订单ID
     * @param refundReason 退款原因
     * @return 结果
     */
    @Override
    @Transactional
    public int refundOrder(Long orderId, String refundReason)
    {
        RecOrder order = new RecOrder();
        order.setOrderId(orderId);
        order.setOrderStatus(OrderStatusEnum.REFUNDED.getCode());
        order.setRemark(refundReason);
        order.setUpdateTime(new Date());
        order.setUpdateBy(SecurityUtils.getUsername());
        
        int result = recOrderMapper.updateRecOrder(order);
        
        // TODO: 调用微信退款API
        
        return result;
    }

    /**
     * 获取订单统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Object getOrderStatistics()
    {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalOrders", recOrderMapper.countTotalOrders());
        statistics.put("paidOrders", recOrderMapper.countPaidOrders());
        statistics.put("pendingPaymentOrders", recOrderMapper.countPendingPaymentOrders());
        statistics.put("todayOrders", recOrderMapper.countTodayOrders());
        statistics.put("totalAmount", recOrderMapper.sumTotalAmount());
        statistics.put("todayAmount", recOrderMapper.sumTodayAmount());
        return statistics;
    }

    /**
     * 查询待支付订单
     * 
     * @return 订单集合
     */
    @Override
    public List<RecOrder> selectPendingPaymentOrders()
    {
        return recOrderMapper.selectPendingPaymentOrders();
    }

    /**
     * 查询已支付订单
     * 
     * @return 订单集合
     */
    @Override
    public List<RecOrder> selectPaidOrders()
    {
        return recOrderMapper.selectPaidOrders();
    }

    /**
     * 处理订单支付成功后的业务逻辑
     * 
     * @param orderNo 订单号
     * @return 处理结果
     */
    @Override
    @Transactional
    public boolean processOrderPaySuccess(String orderNo)
    {
        try {
            RecOrder order = recOrderMapper.selectRecOrderByOrderNo(orderNo);
            if (order == null) {
                return false;
            }
            
            // 更新求职者的查看次数和消费金额
            if (order.getViewCount() != null && order.getViewCount() > 0) {
                recJobSeekerService.addSeekerViewCount(order.getSeekerId(), order.getViewCount());
            }
            
            // 更新求职者的消费金额
            recJobSeekerService.updateSeekerConsumption(order.getSeekerId(), order.getPayAmount());
            
            // 如果是会员套餐，升级会员
            if (order.getOrderType() == 2 && order.getPackageId() != null) {
                recJobSeekerService.upgradeMemberByPackage(order.getSeekerId(), order.getPackageId());
            }
            
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查订单是否过期
     * 
     * @param orderId 订单ID
     * @return 是否过期
     */
    @Override
    public boolean isOrderExpired(Long orderId)
    {
        RecOrder order = recOrderMapper.selectRecOrderByOrderId(orderId);
        if (order == null || order.getExpireTime() == null) {
            return false;
        }
        
        return order.getExpireTime().before(new Date());
    }

    /**
     * 自动取消过期订单
     * 
     * @return 取消的订单数量
     */
    @Override
    @Transactional
    public int cancelExpiredOrders()
    {
        List<RecOrder> expiredOrders = recOrderMapper.selectExpiredOrders();
        int count = 0;
        
        for (RecOrder order : expiredOrders) {
            RecOrder updateOrder = new RecOrder();
            updateOrder.setOrderId(order.getOrderId());
            updateOrder.setOrderStatus(OrderStatusEnum.CANCELLED.getCode());
            updateOrder.setUpdateTime(new Date());
            updateOrder.setRemark("订单超时自动取消");
            
            if (recOrderMapper.updateRecOrder(updateOrder) > 0) {
                count++;
            }
        }
        
        return count;
    }
}
