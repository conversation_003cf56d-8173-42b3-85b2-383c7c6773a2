package com.ruoyi.recruitment.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.recruitment.mapper.RecMemberPackageMapper;
import com.ruoyi.recruitment.domain.RecMemberPackage;
import com.ruoyi.recruitment.service.IRecMemberPackageService;

/**
 * 会员套餐Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class RecMemberPackageServiceImpl implements IRecMemberPackageService 
{
    @Autowired
    private RecMemberPackageMapper recMemberPackageMapper;

    /**
     * 查询会员套餐
     * 
     * @param packageId 会员套餐主键
     * @return 会员套餐
     */
    @Override
    public RecMemberPackage selectRecMemberPackageByPackageId(Long packageId)
    {
        return recMemberPackageMapper.selectRecMemberPackageByPackageId(packageId);
    }

    /**
     * 查询会员套餐列表
     * 
     * @param recMemberPackage 会员套餐
     * @return 会员套餐
     */
    @Override
    public List<RecMemberPackage> selectRecMemberPackageList(RecMemberPackage recMemberPackage)
    {
        return recMemberPackageMapper.selectRecMemberPackageList(recMemberPackage);
    }

    /**
     * 查询启用的会员套餐列表
     * 
     * @return 会员套餐集合
     */
    @Override
    public List<RecMemberPackage> selectActivePackages()
    {
        return recMemberPackageMapper.selectActivePackages();
    }

    /**
     * 根据套餐类型查询会员套餐
     * 
     * @param packageType 套餐类型
     * @return 会员套餐集合
     */
    @Override
    public List<RecMemberPackage> selectPackagesByType(Integer packageType)
    {
        return recMemberPackageMapper.selectPackagesByType(packageType);
    }

    /**
     * 新增会员套餐
     * 
     * @param recMemberPackage 会员套餐
     * @return 结果
     */
    @Override
    public int insertRecMemberPackage(RecMemberPackage recMemberPackage)
    {
        recMemberPackage.setCreateTime(DateUtils.getNowDate());
        return recMemberPackageMapper.insertRecMemberPackage(recMemberPackage);
    }

    /**
     * 修改会员套餐
     * 
     * @param recMemberPackage 会员套餐
     * @return 结果
     */
    @Override
    public int updateRecMemberPackage(RecMemberPackage recMemberPackage)
    {
        recMemberPackage.setUpdateTime(DateUtils.getNowDate());
        return recMemberPackageMapper.updateRecMemberPackage(recMemberPackage);
    }

    /**
     * 批量删除会员套餐
     * 
     * @param packageIds 需要删除的会员套餐主键
     * @return 结果
     */
    @Override
    public int deleteRecMemberPackageByPackageIds(Long[] packageIds)
    {
        return recMemberPackageMapper.deleteRecMemberPackageByPackageIds(packageIds);
    }

    /**
     * 删除会员套餐信息
     * 
     * @param packageId 会员套餐主键
     * @return 结果
     */
    @Override
    public int deleteRecMemberPackageByPackageId(Long packageId)
    {
        return recMemberPackageMapper.deleteRecMemberPackageByPackageId(packageId);
    }

    /**
     * 启用/禁用会员套餐
     * 
     * @param packageId 套餐ID
     * @param isActive 是否启用
     * @return 结果
     */
    @Override
    public int updatePackageStatus(Long packageId, Integer isActive)
    {
        RecMemberPackage memberPackage = new RecMemberPackage();
        memberPackage.setPackageId(packageId);
        memberPackage.setIsActive(isActive);
        memberPackage.setUpdateTime(DateUtils.getNowDate());
        return recMemberPackageMapper.updateRecMemberPackage(memberPackage);
    }

    /**
     * 获取单次查看套餐
     * 
     * @return 单次查看套餐
     */
    @Override
    public RecMemberPackage getSingleViewPackage()
    {
        return recMemberPackageMapper.getSingleViewPackage();
    }

    /**
     * 获取推荐会员套餐
     * 
     * @return 推荐套餐列表
     */
    @Override
    public List<RecMemberPackage> getRecommendedPackages()
    {
        return recMemberPackageMapper.getRecommendedPackages();
    }
}
