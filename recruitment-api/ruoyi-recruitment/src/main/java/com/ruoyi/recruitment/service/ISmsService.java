package com.ruoyi.recruitment.service;

/**
 * 短信服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface ISmsService 
{
    /**
     * 发送验证码
     * 
     * @param phone 手机号
     * @return 结果
     */
    public boolean sendVerificationCode(String phone);

    /**
     * 验证验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 结果
     */
    public boolean verifyCode(String phone, String code);

    /**
     * 发送通知短信
     * 
     * @param phone 手机号
     * @param message 消息内容
     * @return 结果
     */
    public boolean sendNotification(String phone, String message);

    /**
     * 清除验证码缓存
     * 
     * @param phone 手机号
     */
    public void clearVerificationCode(String phone);
}
