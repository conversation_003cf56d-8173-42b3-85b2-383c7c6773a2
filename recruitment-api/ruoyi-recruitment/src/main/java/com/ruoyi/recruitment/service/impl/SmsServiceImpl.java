package com.ruoyi.recruitment.service.impl;

import java.util.Random;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.ruoyi.recruitment.service.ISmsService;

/**
 * 短信服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class SmsServiceImpl implements ISmsService
{
    private static final Logger log = LoggerFactory.getLogger(SmsServiceImpl.class);
    
    private static final String SMS_CODE_PREFIX = "sms:code:";
    private static final String SMS_SEND_PREFIX = "sms:send:";
    private static final int CODE_LENGTH = 6;
    private static final int CODE_EXPIRE_MINUTES = 5;
    private static final int SEND_INTERVAL_SECONDS = 60;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 发送验证码
     * 
     * @param phone 手机号
     * @return 结果
     */
    @Override
    public boolean sendVerificationCode(String phone)
    {
        try {
            // 检查发送频率限制
            String sendKey = SMS_SEND_PREFIX + phone;
            if (redisTemplate.hasKey(sendKey)) {
                log.warn("手机号 {} 发送验证码过于频繁", phone);
                return false;
            }
            
            // 生成6位数字验证码
            String code = generateVerificationCode();
            
            // 存储验证码到Redis，有效期5分钟
            String codeKey = SMS_CODE_PREFIX + phone;
            redisTemplate.opsForValue().set(codeKey, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            
            // 设置发送频率限制，60秒内不能重复发送
            redisTemplate.opsForValue().set(sendKey, "1", SEND_INTERVAL_SECONDS, TimeUnit.SECONDS);
            
            // TODO: 这里应该调用实际的短信服务商API发送短信
            // 例如：阿里云短信、腾讯云短信、华为云短信等
            boolean sendResult = sendSmsToProvider(phone, code);
            
            if (sendResult) {
                log.info("验证码发送成功，手机号: {}, 验证码: {}", phone, code);
                return true;
            } else {
                // 发送失败，清除缓存
                redisTemplate.delete(codeKey);
                redisTemplate.delete(sendKey);
                log.error("验证码发送失败，手机号: {}", phone);
                return false;
            }
        } catch (Exception e) {
            log.error("发送验证码异常，手机号: {}", phone, e);
            return false;
        }
    }

    /**
     * 验证验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 结果
     */
    @Override
    public boolean verifyCode(String phone, String code)
    {
        try {
            String codeKey = SMS_CODE_PREFIX + phone;
            String storedCode = (String) redisTemplate.opsForValue().get(codeKey);
            
            if (storedCode == null) {
                log.warn("验证码不存在或已过期，手机号: {}", phone);
                return false;
            }
            
            if (storedCode.equals(code)) {
                // 验证成功，删除验证码
                redisTemplate.delete(codeKey);
                log.info("验证码验证成功，手机号: {}", phone);
                return true;
            } else {
                log.warn("验证码错误，手机号: {}, 输入: {}, 正确: {}", phone, code, storedCode);
                return false;
            }
        } catch (Exception e) {
            log.error("验证验证码异常，手机号: {}", phone, e);
            return false;
        }
    }

    /**
     * 发送通知短信
     * 
     * @param phone 手机号
     * @param message 消息内容
     * @return 结果
     */
    @Override
    public boolean sendNotification(String phone, String message)
    {
        try {
            // TODO: 调用短信服务商API发送通知短信
            boolean sendResult = sendNotificationToProvider(phone, message);
            
            if (sendResult) {
                log.info("通知短信发送成功，手机号: {}, 内容: {}", phone, message);
                return true;
            } else {
                log.error("通知短信发送失败，手机号: {}", phone);
                return false;
            }
        } catch (Exception e) {
            log.error("发送通知短信异常，手机号: {}", phone, e);
            return false;
        }
    }

    /**
     * 清除验证码缓存
     * 
     * @param phone 手机号
     */
    @Override
    public void clearVerificationCode(String phone)
    {
        try {
            String codeKey = SMS_CODE_PREFIX + phone;
            redisTemplate.delete(codeKey);
            log.info("清除验证码缓存成功，手机号: {}", phone);
        } catch (Exception e) {
            log.error("清除验证码缓存异常，手机号: {}", phone, e);
        }
    }

    /**
     * 生成验证码
     */
    private String generateVerificationCode()
    {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    /**
     * 调用短信服务商发送验证码
     * TODO: 实现具体的短信服务商API调用
     */
    private boolean sendSmsToProvider(String phone, String code)
    {
        // 这里应该调用实际的短信服务商API
        // 例如：阿里云短信、腾讯云短信等
        
        // 模拟发送成功
        log.info("模拟发送验证码短信，手机号: {}, 验证码: {}", phone, code);
        return true;
    }

    /**
     * 调用短信服务商发送通知短信
     * TODO: 实现具体的短信服务商API调用
     */
    private boolean sendNotificationToProvider(String phone, String message)
    {
        // 这里应该调用实际的短信服务商API
        
        // 模拟发送成功
        log.info("模拟发送通知短信，手机号: {}, 内容: {}", phone, message);
        return true;
    }
}
