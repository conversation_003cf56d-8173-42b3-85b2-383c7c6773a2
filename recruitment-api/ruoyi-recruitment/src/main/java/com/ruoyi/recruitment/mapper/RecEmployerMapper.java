package com.ruoyi.recruitment.mapper;

import java.util.List;
import com.ruoyi.recruitment.domain.RecEmployer;

/**
 * 商家Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface RecEmployerMapper 
{
    /**
     * 查询商家
     * 
     * @param employerId 商家主键
     * @return 商家
     */
    public RecEmployer selectRecEmployerByEmployerId(Long employerId);

    /**
     * 根据登录账号查询商家
     * 
     * @param loginAccount 登录账号
     * @return 商家
     */
    public RecEmployer selectRecEmployerByLoginAccount(String loginAccount);

    /**
     * 根据联系电话查询商家
     * 
     * @param contactPhone 联系电话
     * @return 商家
     */
    public RecEmployer selectRecEmployerByContactPhone(String contactPhone);

    /**
     * 查询商家列表
     * 
     * @param recEmployer 商家
     * @return 商家集合
     */
    public List<RecEmployer> selectRecEmployerList(RecEmployer recEmployer);

    /**
     * 新增商家
     * 
     * @param recEmployer 商家
     * @return 结果
     */
    public int insertRecEmployer(RecEmployer recEmployer);

    /**
     * 修改商家
     * 
     * @param recEmployer 商家
     * @return 结果
     */
    public int updateRecEmployer(RecEmployer recEmployer);

    /**
     * 删除商家
     * 
     * @param employerId 商家主键
     * @return 结果
     */
    public int deleteRecEmployerByEmployerId(Long employerId);

    /**
     * 批量删除商家
     * 
     * @param employerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecEmployerByEmployerIds(Long[] employerIds);

    /**
     * 更新商家审核状态
     * 
     * @param recEmployer 商家信息
     * @return 结果
     */
    public int updateEmployerAuditStatus(RecEmployer recEmployer);

    /**
     * 统计商家总数
     * 
     * @return 总数
     */
    public int countTotalEmployers();

    /**
     * 统计待审核商家数量
     * 
     * @return 待审核数量
     */
    public int countPendingAuditEmployers();

    /**
     * 统计已通过审核商家数量
     * 
     * @return 已通过数量
     */
    public int countApprovedEmployers();

    /**
     * 统计今日新增商家数量
     * 
     * @return 今日新增数量
     */
    public int countTodayNewEmployers();

    /**
     * 查询待审核商家列表
     * 
     * @return 商家集合
     */
    public List<RecEmployer> selectPendingAuditEmployers();
}
