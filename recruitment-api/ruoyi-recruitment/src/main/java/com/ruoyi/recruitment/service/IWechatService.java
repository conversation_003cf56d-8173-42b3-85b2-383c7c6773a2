package com.ruoyi.recruitment.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.recruitment.domain.RecJobSeeker;
import com.ruoyi.recruitment.domain.RecOrder;

/**
 * 微信服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IWechatService 
{
    /**
     * 微信授权登录
     * 
     * @param code 微信授权码
     * @return 求职者信息
     */
    public RecJobSeeker wechatLogin(String code);

    /**
     * 手机号一键登录
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 求职者信息
     */
    public RecJobSeeker phoneLogin(String phone, String code);

    /**
     * 获取微信用户信息
     * 
     * @param code 微信授权码
     * @return 用户信息
     */
    public Map<String, Object> getWechatUserInfo(String code);

    /**
     * 查看联系方式
     * 
     * @param positionId 职位ID
     * @param seekerId 求职者ID
     * @return 联系方式信息
     */
    public Map<String, Object> viewContact(Long positionId, Long seekerId);

    /**
     * 创建订单
     * 
     * @param recOrder 订单信息
     * @return 订单
     */
    public RecOrder createOrder(RecOrder recOrder);

    /**
     * 创建微信支付
     * 
     * @param orderNo 订单号
     * @param openid 用户openid
     * @return 支付信息
     */
    public Map<String, Object> createWechatPay(String orderNo, String openid);

    /**
     * 处理微信支付回调
     * 
     * @param xmlData 回调数据
     * @return 处理结果
     */
    public boolean handlePayNotify(String xmlData);

    /**
     * 查询支付状态
     * 
     * @param orderNo 订单号
     * @return 支付状态
     */
    public Map<String, Object> queryPayStatus(String orderNo);

    /**
     * 获取用户查看记录
     * 
     * @param seekerId 求职者ID
     * @return 查看记录列表
     */
    public List<Map<String, Object>> getUserViewRecords(Long seekerId);

    /**
     * 获取用户会员状态
     * 
     * @param seekerId 求职者ID
     * @return 会员状态信息
     */
    public Map<String, Object> getMemberStatus(Long seekerId);

    /**
     * 获取地区列表
     * 
     * @param parentCode 父级地区编码
     * @return 地区列表
     */
    public List<Map<String, Object>> getRegions(String parentCode);

    /**
     * 发送模板消息
     * 
     * @param openid 用户openid
     * @param templateId 模板ID
     * @param data 消息数据
     * @return 发送结果
     */
    public boolean sendTemplateMessage(String openid, String templateId, Map<String, Object> data);

    /**
     * 生成小程序码
     * 
     * @param scene 场景值
     * @param page 页面路径
     * @return 小程序码图片数据
     */
    public byte[] generateMiniCode(String scene, String page);

    /**
     * 获取访问令牌
     * 
     * @return 访问令牌
     */
    public String getAccessToken();

    /**
     * 解密微信数据
     * 
     * @param encryptedData 加密数据
     * @param iv 初始向量
     * @param sessionKey 会话密钥
     * @return 解密后的数据
     */
    public Map<String, Object> decryptWechatData(String encryptedData, String iv, String sessionKey);

    /**
     * 验证微信签名
     * 
     * @param signature 签名
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @return 验证结果
     */
    public boolean verifySignature(String signature, String timestamp, String nonce);

    /**
     * 获取用户手机号
     * 
     * @param code 手机号授权码
     * @return 手机号信息
     */
    public Map<String, Object> getPhoneNumber(String code);

    /**
     * 统计小程序访问数据
     * 
     * @param openid 用户openid
     * @param page 页面路径
     * @param action 操作类型
     */
    public void recordVisit(String openid, String page, String action);

    /**
     * 获取小程序访问统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    public Map<String, Object> getVisitStatistics(String startDate, String endDate);
}
