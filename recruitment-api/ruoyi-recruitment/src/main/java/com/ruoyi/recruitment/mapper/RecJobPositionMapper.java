package com.ruoyi.recruitment.mapper;

import java.util.List;
import com.ruoyi.recruitment.domain.RecJobPosition;

/**
 * 职位Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface RecJobPositionMapper 
{
    /**
     * 查询职位
     * 
     * @param positionId 职位主键
     * @return 职位
     */
    public RecJobPosition selectRecJobPositionByPositionId(Long positionId);

    /**
     * 查询职位列表
     * 
     * @param recJobPosition 职位
     * @return 职位集合
     */
    public List<RecJobPosition> selectRecJobPositionList(RecJobPosition recJobPosition);

    /**
     * 根据商家ID查询职位列表
     * 
     * @param employerId 商家ID
     * @return 职位集合
     */
    public List<RecJobPosition> selectRecJobPositionByEmployerId(Long employerId);

    /**
     * 新增职位
     * 
     * @param recJobPosition 职位
     * @return 结果
     */
    public int insertRecJobPosition(RecJobPosition recJobPosition);

    /**
     * 修改职位
     * 
     * @param recJobPosition 职位
     * @return 结果
     */
    public int updateRecJobPosition(RecJobPosition recJobPosition);

    /**
     * 删除职位
     * 
     * @param positionId 职位主键
     * @return 结果
     */
    public int deleteRecJobPositionByPositionId(Long positionId);

    /**
     * 批量删除职位
     * 
     * @param positionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecJobPositionByPositionIds(Long[] positionIds);

    /**
     * 更新职位审核状态
     * 
     * @param recJobPosition 职位信息
     * @return 结果
     */
    public int updatePositionAuditStatus(RecJobPosition recJobPosition);

    /**
     * 更新职位发布状态
     * 
     * @param recJobPosition 职位信息
     * @return 结果
     */
    public int updatePositionPublishStatus(RecJobPosition recJobPosition);

    /**
     * 增加职位浏览次数
     * 
     * @param positionId 职位ID
     * @return 结果
     */
    public int increaseViewCount(Long positionId);

    /**
     * 增加职位申请次数
     * 
     * @param positionId 职位ID
     * @return 结果
     */
    public int increaseApplyCount(Long positionId);

    /**
     * 统计职位总数
     * 
     * @return 总数
     */
    public int countTotalPositions();

    /**
     * 统计已发布职位数量
     * 
     * @return 已发布数量
     */
    public int countPublishedPositions();

    /**
     * 统计待审核职位数量
     * 
     * @return 待审核数量
     */
    public int countPendingAuditPositions();

    /**
     * 统计今日新增职位数量
     * 
     * @return 今日新增数量
     */
    public int countTodayNewPositions();

    /**
     * 查询待审核职位列表
     * 
     * @return 职位集合
     */
    public List<RecJobPosition> selectPendingAuditPositions();

    /**
     * 查询热门职位列表
     * 
     * @param limit 限制数量
     * @return 职位集合
     */
    public List<RecJobPosition> selectHotPositions(Integer limit);

    /**
     * 查询最新职位列表
     * 
     * @param limit 限制数量
     * @return 职位集合
     */
    public List<RecJobPosition> selectLatestPositions(Integer limit);

    /**
     * 根据地区查询职位列表
     * 
     * @param regionCode 地区编码
     * @return 职位集合
     */
    public List<RecJobPosition> selectPositionsByRegion(String regionCode);

    /**
     * 搜索职位
     * 
     * @param keyword 关键词
     * @param regionCode 地区编码
     * @return 职位集合
     */
    public List<RecJobPosition> searchPositions(String keyword, String regionCode);
}
