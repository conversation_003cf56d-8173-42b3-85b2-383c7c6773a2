package com.ruoyi.recruitment.enums;

/**
 * 会员类型枚举
 * 
 * <AUTHOR>
 */
public enum MemberTypeEnum {
    
    NORMAL(0, "普通用户"),
    MEMBER_30(1, "30元会员"),
    MEMBER_50(2, "50元会员");
    
    private final Integer code;
    private final String desc;
    
    MemberTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static MemberTypeEnum getByCode(Integer code) {
        for (MemberTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return NORMAL;
    }
}
