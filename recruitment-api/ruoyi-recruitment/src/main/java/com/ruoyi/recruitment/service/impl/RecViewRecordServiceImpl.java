package com.ruoyi.recruitment.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.recruitment.mapper.RecViewRecordMapper;
import com.ruoyi.recruitment.domain.RecViewRecord;
import com.ruoyi.recruitment.service.IRecViewRecordService;

/**
 * 查看记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class RecViewRecordServiceImpl implements IRecViewRecordService 
{
    @Autowired
    private RecViewRecordMapper recViewRecordMapper;

    /**
     * 查询查看记录
     * 
     * @param recordId 查看记录主键
     * @return 查看记录
     */
    @Override
    public RecViewRecord selectRecViewRecordByRecordId(Long recordId)
    {
        return recViewRecordMapper.selectRecViewRecordByRecordId(recordId);
    }

    /**
     * 查询查看记录列表
     * 
     * @param recViewRecord 查看记录
     * @return 查看记录
     */
    @Override
    public List<RecViewRecord> selectRecViewRecordList(RecViewRecord recViewRecord)
    {
        return recViewRecordMapper.selectRecViewRecordList(recViewRecord);
    }

    /**
     * 根据求职者ID查询查看记录
     * 
     * @param seekerId 求职者ID
     * @return 查看记录集合
     */
    @Override
    public List<Map<String, Object>> selectViewRecordsBySeekerId(Long seekerId)
    {
        return recViewRecordMapper.selectViewRecordsBySeekerId(seekerId);
    }

    /**
     * 根据职位ID查询查看记录
     * 
     * @param positionId 职位ID
     * @return 查看记录集合
     */
    @Override
    public List<RecViewRecord> selectViewRecordsByPositionId(Long positionId)
    {
        return recViewRecordMapper.selectViewRecordsByPositionId(positionId);
    }

    /**
     * 根据商家ID查询查看记录
     * 
     * @param employerId 商家ID
     * @return 查看记录集合
     */
    @Override
    public List<RecViewRecord> selectViewRecordsByEmployerId(Long employerId)
    {
        return recViewRecordMapper.selectViewRecordsByEmployerId(employerId);
    }

    /**
     * 新增查看记录
     * 
     * @param recViewRecord 查看记录
     * @return 结果
     */
    @Override
    public int insertRecViewRecord(RecViewRecord recViewRecord)
    {
        recViewRecord.setCreateTime(DateUtils.getNowDate());
        return recViewRecordMapper.insertRecViewRecord(recViewRecord);
    }

    /**
     * 修改查看记录
     * 
     * @param recViewRecord 查看记录
     * @return 结果
     */
    @Override
    public int updateRecViewRecord(RecViewRecord recViewRecord)
    {
        return recViewRecordMapper.updateRecViewRecord(recViewRecord);
    }

    /**
     * 批量删除查看记录
     * 
     * @param recordIds 需要删除的查看记录主键
     * @return 结果
     */
    @Override
    public int deleteRecViewRecordByRecordIds(Long[] recordIds)
    {
        return recViewRecordMapper.deleteRecViewRecordByRecordIds(recordIds);
    }

    /**
     * 删除查看记录信息
     * 
     * @param recordId 查看记录主键
     * @return 结果
     */
    @Override
    public int deleteRecViewRecordByRecordId(Long recordId)
    {
        return recViewRecordMapper.deleteRecViewRecordByRecordId(recordId);
    }

    /**
     * 检查用户是否已查看过该职位
     * 
     * @param seekerId 求职者ID
     * @param positionId 职位ID
     * @return 是否已查看
     */
    @Override
    public boolean hasViewedPosition(Long seekerId, Long positionId)
    {
        int count = recViewRecordMapper.countViewRecord(seekerId, positionId);
        return count > 0;
    }

    /**
     * 获取查看记录统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Object getViewRecordStatistics()
    {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalViews", recViewRecordMapper.countTotalViews());
        statistics.put("todayViews", recViewRecordMapper.countTodayViews());
        statistics.put("paidViews", recViewRecordMapper.countPaidViews());
        statistics.put("memberViews", recViewRecordMapper.countMemberViews());
        statistics.put("totalRevenue", recViewRecordMapper.sumTotalRevenue());
        statistics.put("todayRevenue", recViewRecordMapper.sumTodayRevenue());
        return statistics;
    }

    /**
     * 获取热门职位查看统计
     * 
     * @param limit 限制数量
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> getHotPositionViewStats(Integer limit)
    {
        return recViewRecordMapper.getHotPositionViewStats(limit);
    }

    /**
     * 获取用户消费统计
     * 
     * @param seekerId 求职者ID
     * @return 消费统计
     */
    @Override
    public Map<String, Object> getUserConsumptionStats(Long seekerId)
    {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalViews", recViewRecordMapper.countUserTotalViews(seekerId));
        stats.put("paidViews", recViewRecordMapper.countUserPaidViews(seekerId));
        stats.put("memberViews", recViewRecordMapper.countUserMemberViews(seekerId));
        stats.put("totalCost", recViewRecordMapper.sumUserTotalCost(seekerId));
        return stats;
    }
}
