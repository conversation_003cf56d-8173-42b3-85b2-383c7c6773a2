<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.recruitment.mapper.RecOrderMapper">
    
    <resultMap type="RecOrder" id="RecOrderResult">
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="seekerId"    column="seeker_id"    />
        <result property="packageId"    column="package_id"    />
        <result property="orderType"    column="order_type"    />
        <result property="orderAmount"    column="order_amount"    />
        <result property="payAmount"    column="pay_amount"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="payStatus"    column="pay_status"    />
        <result property="payType"    column="pay_type"    />
        <result property="payTime"    column="pay_time"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="viewCount"    column="view_count"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRecOrderVo">
        select order_id, order_no, seeker_id, package_id, order_type, order_amount, pay_amount, order_status, pay_status, pay_type, pay_time, transaction_id, view_count, expire_time, remark, create_by, create_time, update_by, update_time from rec_order
    </sql>

    <select id="selectRecOrderList" parameterType="RecOrder" resultMap="RecOrderResult">
        <include refid="selectRecOrderVo"/>
        <where>  
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="seekerId != null "> and seeker_id = #{seekerId}</if>
            <if test="packageId != null "> and package_id = #{packageId}</if>
            <if test="orderType != null "> and order_type = #{orderType}</if>
            <if test="orderStatus != null "> and order_status = #{orderStatus}</if>
            <if test="payStatus != null "> and pay_status = #{payStatus}</if>
            <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectRecOrderByOrderId" parameterType="Long" resultMap="RecOrderResult">
        <include refid="selectRecOrderVo"/>
        where order_id = #{orderId}
    </select>

    <select id="selectRecOrderByOrderNo" parameterType="String" resultMap="RecOrderResult">
        <include refid="selectRecOrderVo"/>
        where order_no = #{orderNo}
    </select>

    <select id="selectOrdersBySeekerId" parameterType="Long" resultMap="RecOrderResult">
        <include refid="selectRecOrderVo"/>
        where seeker_id = #{seekerId}
        order by create_time desc
    </select>

    <insert id="insertRecOrder" parameterType="RecOrder" useGeneratedKeys="true" keyProperty="orderId">
        insert into rec_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="seekerId != null">seeker_id,</if>
            <if test="packageId != null">package_id,</if>
            <if test="orderType != null">order_type,</if>
            <if test="orderAmount != null">order_amount,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="seekerId != null">#{seekerId},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="orderAmount != null">#{orderAmount},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRecOrder" parameterType="RecOrder">
        update rec_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="seekerId != null">seeker_id = #{seekerId},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="orderAmount != null">order_amount = #{orderAmount},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where order_id = #{orderId}
    </update>

    <update id="updateOrderByOrderNo" parameterType="RecOrder">
        update rec_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where order_no = #{orderNo}
    </update>

    <delete id="deleteRecOrderByOrderId" parameterType="Long">
        delete from rec_order where order_id = #{orderId}
    </delete>

    <delete id="deleteRecOrderByOrderIds" parameterType="String">
        delete from rec_order where order_id in 
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

    <!-- 统计查询 -->
    <select id="countTotalOrders" resultType="int">
        select count(*) from rec_order
    </select>

    <select id="countPaidOrders" resultType="int">
        select count(*) from rec_order where pay_status = 1
    </select>

    <select id="countPendingPaymentOrders" resultType="int">
        select count(*) from rec_order where order_status = 0
    </select>

    <select id="countTodayOrders" resultType="int">
        select count(*) from rec_order where date_format(create_time,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
    </select>

    <select id="sumTotalAmount" resultType="java.math.BigDecimal">
        select IFNULL(sum(pay_amount), 0) from rec_order where pay_status = 1
    </select>

    <select id="sumTodayAmount" resultType="java.math.BigDecimal">
        select IFNULL(sum(pay_amount), 0) from rec_order where pay_status = 1 and date_format(create_time,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
    </select>

    <select id="selectPendingPaymentOrders" resultMap="RecOrderResult">
        <include refid="selectRecOrderVo"/>
        where order_status = 0
        order by create_time desc
    </select>

    <select id="selectPaidOrders" resultMap="RecOrderResult">
        <include refid="selectRecOrderVo"/>
        where pay_status = 1
        order by pay_time desc
    </select>

    <select id="selectExpiredOrders" resultMap="RecOrderResult">
        <include refid="selectRecOrderVo"/>
        where order_status = 0 and expire_time &lt; now()
    </select>

</mapper>
