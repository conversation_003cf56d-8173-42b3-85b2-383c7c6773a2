<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.recruitment.mapper.RecJobSeekerMapper">
    
    <resultMap type="RecJobSeeker" id="RecJobSeekerResult">
        <result property="seekerId"    column="seeker_id"    />
        <result property="openid"    column="openid"    />
        <result property="unionid"    column="unionid"    />
        <result property="phone"    column="phone"    />
        <result property="nickname"    column="nickname"    />
        <result property="avatar"    column="avatar"    />
        <result property="gender"    column="gender"    />
        <result property="regionCode"    column="region_code"    />
        <result property="memberType"    column="member_type"    />
        <result property="memberExpireTime"    column="member_expire_time"    />
        <result property="viewCount"    column="view_count"    />
        <result property="totalConsumed"    column="total_consumed"    />
        <result property="status"    column="status"    />
        <result property="registerTime"    column="register_time"    />
        <result property="lastLoginTime"    column="last_login_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="regionName"    column="region_name"    />
    </resultMap>

    <sql id="selectRecJobSeekerVo">
        select s.seeker_id, s.openid, s.unionid, s.phone, s.nickname, s.avatar, s.gender, 
               s.region_code, s.member_type, s.member_expire_time, s.view_count, s.total_consumed, 
               s.status, s.register_time, s.last_login_time, s.create_by, s.create_time, 
               s.update_by, s.update_time, r.region_name
        from rec_job_seeker s
        left join rec_region r on s.region_code = r.region_code
    </sql>

    <select id="selectRecJobSeekerList" parameterType="RecJobSeeker" resultMap="RecJobSeekerResult">
        <include refid="selectRecJobSeekerVo"/>
        <where>  
            <if test="openid != null  and openid != ''"> and s.openid = #{openid}</if>
            <if test="phone != null  and phone != ''"> and s.phone = #{phone}</if>
            <if test="nickname != null  and nickname != ''"> and s.nickname like concat('%', #{nickname}, '%')</if>
            <if test="gender != null "> and s.gender = #{gender}</if>
            <if test="regionCode != null  and regionCode != ''"> and s.region_code = #{regionCode}</if>
            <if test="memberType != null "> and s.member_type = #{memberType}</if>
            <if test="status != null  and status != ''"> and s.status = #{status}</if>
            <if test="params.beginRegisterTime != null and params.beginRegisterTime != ''"><!-- 开始注册时间 -->
                and date_format(s.register_time,'%y%m%d') &gt;= date_format(#{params.beginRegisterTime},'%y%m%d')
            </if>
            <if test="params.endRegisterTime != null and params.endRegisterTime != ''"><!-- 结束注册时间 -->
                and date_format(s.register_time,'%y%m%d') &lt;= date_format(#{params.endRegisterTime},'%y%m%d')
            </if>
        </where>
        order by s.create_time desc
    </select>
    
    <select id="selectRecJobSeekerBySeekerId" parameterType="Long" resultMap="RecJobSeekerResult">
        <include refid="selectRecJobSeekerVo"/>
        where s.seeker_id = #{seekerId}
    </select>

    <select id="selectRecJobSeekerByOpenid" parameterType="String" resultMap="RecJobSeekerResult">
        <include refid="selectRecJobSeekerVo"/>
        where s.openid = #{openid}
    </select>

    <select id="selectRecJobSeekerByPhone" parameterType="String" resultMap="RecJobSeekerResult">
        <include refid="selectRecJobSeekerVo"/>
        where s.phone = #{phone}
    </select>
        
    <insert id="insertRecJobSeeker" parameterType="RecJobSeeker" useGeneratedKeys="true" keyProperty="seekerId">
        insert into rec_job_seeker
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid,</if>
            <if test="unionid != null and unionid != ''">unionid,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="gender != null">gender,</if>
            <if test="regionCode != null and regionCode != ''">region_code,</if>
            <if test="memberType != null">member_type,</if>
            <if test="memberExpireTime != null">member_expire_time,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="totalConsumed != null">total_consumed,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="lastLoginTime != null">last_login_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="unionid != null and unionid != ''">#{unionid},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="gender != null">#{gender},</if>
            <if test="regionCode != null and regionCode != ''">#{regionCode},</if>
            <if test="memberType != null">#{memberType},</if>
            <if test="memberExpireTime != null">#{memberExpireTime},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="totalConsumed != null">#{totalConsumed},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="lastLoginTime != null">#{lastLoginTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRecJobSeeker" parameterType="RecJobSeeker">
        update rec_job_seeker
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="unionid != null and unionid != ''">unionid = #{unionid},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="regionCode != null and regionCode != ''">region_code = #{regionCode},</if>
            <if test="memberType != null">member_type = #{memberType},</if>
            <if test="memberExpireTime != null">member_expire_time = #{memberExpireTime},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="totalConsumed != null">total_consumed = #{totalConsumed},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where seeker_id = #{seekerId}
    </update>

    <delete id="deleteRecJobSeekerBySeekerId" parameterType="Long">
        delete from rec_job_seeker where seeker_id = #{seekerId}
    </delete>

    <delete id="deleteRecJobSeekerBySeekerIds" parameterType="String">
        delete from rec_job_seeker where seeker_id in 
        <foreach item="seekerId" collection="array" open="(" separator="," close=")">
            #{seekerId}
        </foreach>
    </delete>

    <update id="updateSeekerViewCount">
        update rec_job_seeker 
        set view_count = view_count + #{viewCount}
        where seeker_id = #{seekerId}
    </update>

    <update id="updateSeekerMemberInfo" parameterType="RecJobSeeker">
        update rec_job_seeker
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberType != null">member_type = #{memberType},</if>
            <if test="memberExpireTime != null">member_expire_time = #{memberExpireTime},</if>
            <if test="viewCount != null">view_count = view_count + #{viewCount},</if>
        </trim>
        where seeker_id = #{seekerId}
    </update>

    <update id="updateSeekerConsumption">
        update rec_job_seeker
        set total_consumed = total_consumed + #{amount}
        where seeker_id = #{seekerId}
    </update>

    <select id="countTotalSeekers" resultType="int">
        select count(*) from rec_job_seeker where status = '0'
    </select>

    <select id="countMemberSeekers" resultType="int">
        select count(*) from rec_job_seeker 
        where status = '0' and member_type > 0 
        and (member_expire_time is null or member_expire_time > now())
    </select>

    <select id="countTodayNewSeekers" resultType="int">
        select count(*) from rec_job_seeker 
        where date_format(register_time,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
    </select>

</mapper>
