-- 更新微信小程序配置
-- 执行此脚本来更新现有数据库中的微信配置

-- 更新微信小程序AppID（从manifest.json中获取）
UPDATE `rec_system_config` 
SET `config_value` = 'wx345155b124695971', 
    `update_time` = NOW(), 
    `update_by` = 'admin'
WHERE `config_key` = 'wechat.appid';

-- 更新微信小程序Secret（需要从微信公众平台获取）
-- 注意：这里需要替换为真实的Secret值
UPDATE `rec_system_config` 
SET `config_desc` = '微信小程序Secret（请在微信公众平台获取真实值）',
    `update_time` = NOW(), 
    `update_by` = 'admin'
WHERE `config_key` = 'wechat.secret';

-- 如果配置不存在，则插入新配置
INSERT IGNORE INTO `rec_system_config` 
(`config_key`, `config_value`, `config_type`, `config_desc`, `is_system`, `create_by`, `create_time`) 
VALUES 
('wechat.appid', 'wx345155b124695971', 'string', '微信小程序AppID', 1, 'admin', NOW()),
('wechat.secret', '', 'string', '微信小程序Secret（请在微信公众平台获取真实值）', 1, 'admin', NOW());

-- 查询当前微信配置
SELECT * FROM `rec_system_config` WHERE `config_key` LIKE 'wechat.%';
